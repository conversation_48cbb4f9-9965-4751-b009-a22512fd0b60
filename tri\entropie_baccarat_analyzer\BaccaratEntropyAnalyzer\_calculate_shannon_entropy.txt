# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 163 à 187
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _calculate_shannon_entropy(analyzer::BaccaratEntropyAnalyzer, probabilities::Vector{Float64})::Float64

📊 SHANNON - Calcule l'entropie de Shannon: H(X) = -∑ p(x) log₂ p(x)

Formule fondamentale de la théorie de l'information pour mesurer
l'incertitude d'une distribution de probabilités.

Référence: entropie/cours_entropie/niveau_debutant/02_formule_shannon.md

# Arguments
- `probabilities::Vector{Float64}`: Liste des probabilités

# Returns
- `Float64`: Entropie de Shannon en bits
"""
function _calculate_shannon_entropy(analyzer::BaccaratEntropyAnalyzer, probabilities::Vector{Float64})::Float64
    p = _validate_probabilities(analyzer, probabilities)

    # Calcul avec gestion de 0*log(0) = 0
    log_p = _safe_log(analyzer, p)
    entropy_terms = p .* log_p

    # Remplace NaN par 0 (cas 0*log(0))
    entropy_terms = [p[i] == 0 ? 0.0 : entropy_terms[i] for i in 1:length(p)]

    return -sum(entropy_terms)
end