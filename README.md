# 🎰 Baccarat Entropy Analyzer - INDEX5

Système d'analyse entropique avancée pour le baccarat utilisant la méthode INDEX5.

## 📋 Description

Ce package Julia fournit une analyse entropique complète des parties de baccarat en utilisant:
- Les formules d'entropie de Shannon
- L'analyse différentielle prédictive
- Les métriques INDEX5 spécialisées
- La validation de prédictions
- L'interface utilisateur interactive

## 🏗️ Architecture des Modules

Le système est composé de 9 modules Julia interconnectés:

### 1. **BaccaratEntropyAnalyzer** (Module de base)
- Calculs d'entropie fondamentaux
- Entropie de Shannon, conditionnelle, topologique
- Complexité LZ, entropie métrique
- Export et visualisation

### 2. **INDEX5Calculator**
- Métriques INDEX5 spécialisées
- Prédictibilité contextuelle
- Force des patterns et consensus multi-algorithmes
- Dépend de: `BaccaratEntropyAnalyzer`

### 3. **INDEX5DifferentialAnalyzer**
- Analyse des différentiels entre mains consécutives
- Statistiques des variations entropiques
- Module indépendant

### 4. **INDEX5PredictionValidator**
- Validation des prédictions INDEX5
- Comparaison INDEX3 (BANKER/PLAYER/TIE)
- Statistiques de précision
- Module indépendant

### 5. **INDEX5PredictiveDifferentialTable**
- Tableaux différentiels prédictifs
- Simulation des 9 valeurs INDEX5 possibles
- Règles INDEX1 déterministes
- Dépend de: `BaccaratEntropyAnalyzer`

### 6. **INDEX5PredictiveScoreCalculator**
- Calcul des scores prédictifs
- Formule: `SCORE = (DiffC + EntG) / (DiffT + DivEG)`
- Module indépendant

### 7. **INDEX5PredictiveScoreTable**
- Tableaux de scores prédictifs
- Synchronisation avec les différentiels
- Dépend de: `INDEX5PredictiveScoreCalculator`, `INDEX5PredictiveDifferentialTable`

### 8. **INDEX5Predictor**
- Prédicteur principal INDEX5
- Fusion multi-algorithmes avec vote pondéré
- Contraintes INDEX1 déterministes
- Module indépendant

### 9. **General**
- Interface utilisateur interactive
- Point d'entrée principal
- Orchestration de tous les modules
- Dépend de: tous les autres modules

## 🚀 Installation et Utilisation

### Installation
```bash
cd C:\Users\<USER>\Desktop\11\entropie_baccarat_analyzer_julia_groupe
```

### Test des modules
```bash
julia test_modules.jl
```

### Utilisation interactive
```julia
julia> include("BaccaratEntropyAnalyzer.jl")
julia> using .BaccaratEntropyAnalyzer
julia> main()
```

### Utilisation avancée
```julia
# Accès direct aux modules
julia> analyzer = BEA.BaccaratEntropyAnalyzer()
julia> calculator = I5C.INDEX5Calculator(analyzer)
julia> predictor = I5P.INDEX5Predictor()
```

## 📊 Fonctionnalités Principales

### Analyse Entropique
- ✅ Entropie de Shannon
- ✅ Entropie conditionnelle
- ✅ Entropie topologique
- ✅ Complexité LZ
- ✅ Entropie métrique

### Prédiction INDEX5
- ✅ Contraintes INDEX1 déterministes
- ✅ Fusion multi-algorithmes
- ✅ Vote pondéré intelligent
- ✅ Validation automatique

### Tableaux Prédictifs
- ✅ Différentiels pour 9 valeurs INDEX5
- ✅ Scores prédictifs calculés
- ✅ Synchronisation parfaite
- ✅ Export formaté

### Interface Utilisateur
- ✅ Menu interactif
- ✅ Analyse de parties spécifiques
- ✅ Statistiques théoriques
- ✅ Visualisation graphique

## 🔧 Dépendances

### Obligatoires
- `Statistics` (Julia standard)
- `Printf` (Julia standard)

### Optionnelles
- `DataFrames` (pour export CSV)
- `CSV` (pour export CSV)
- `Plots` (pour visualisation)

## 📁 Structure des Fichiers

```
entropie_baccarat_analyzer_julia_groupe/
├── BaccaratEntropyAnalyzer.jl          # Package principal
├── Project.toml                        # Configuration du package
├── README.md                           # Documentation
├── test_modules.jl                     # Tests
├── BaccaratEntropyAnalyzer/
│   ├── BaccaratEntropyAnalyzer.txt     # Code source
│   └── BaccaratEntropyAnalyzer.jl      # Module Julia
├── INDEX5Calculator/
│   ├── INDEX5Calculator.txt
│   └── INDEX5Calculator.jl
├── INDEX5DifferentialAnalyzer/
│   ├── INDEX5DifferentialAnalyzer.txt
│   └── INDEX5DifferentialAnalyzer.jl
├── INDEX5PredictionValidator/
│   ├── INDEX5PredictionValidator.txt
│   └── INDEX5PredictionValidator.jl
├── INDEX5PredictiveDifferentialTable/
│   ├── INDEX5PredictiveDifferentialTable.txt
│   └── INDEX5PredictiveDifferentialTable.jl
├── INDEX5PredictiveScoreCalculator/
│   ├── INDEX5PredictiveScoreCalculator.txt
│   └── INDEX5PredictiveScoreCalculator.jl
├── INDEX5PredictiveScoreTable/
│   ├── INDEX5PredictiveScoreTable.txt
│   └── INDEX5PredictiveScoreTable.jl
├── INDEX5Predictor/
│   ├── INDEX5Predictor.txt
│   └── INDEX5Predictor.jl
└── General/
    ├── General.txt
    └── General.jl
```

## 🎯 Utilisation Recommandée

1. **Débutants**: Utilisez `main()` pour l'interface interactive
2. **Avancés**: Accédez directement aux modules spécialisés
3. **Développeurs**: Modifiez les fichiers `.txt` puis rechargez les modules

## 📈 Performance

- **Optimisé** pour l'analyse en temps réel
- **Cache intelligent** pour éviter les recalculs
- **Mémoire efficace** avec structures optimisées
- **Parallélisation** possible pour analyses batch

---

*Développé pour l'analyse entropique avancée du baccarat INDEX5*
