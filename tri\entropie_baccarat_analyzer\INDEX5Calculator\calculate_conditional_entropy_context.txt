# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1400 à 1446
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_conditional_entropy_context(calculator::INDEX5Calculator, sequence_history::Vector{String}, context_length::Int=3)::Float64

Calcule l'entropie conditionnelle pour différents contextes.
Retourne l'entropie conditionnelle moyenne pour le contexte donné.
"""
function calculate_conditional_entropy_context(calculator::INDEX5Calculator, sequence_history::Vector{String}, context_length::Int=3)::Float64
    if length(sequence_history) <= context_length
        return 0.0
    end

    # Analyser les transitions depuis les contextes de longueur donnée
    context_transitions = Dict{Tuple, Dict{String, Int}}()

    for i in (context_length + 1):length(sequence_history)
        context = Tuple(sequence_history[i-context_length:i-1])
        next_value = sequence_history[i]

        if context ∉ keys(context_transitions)
            context_transitions[context] = Dict{String, Int}()
        end

        if next_value ∉ keys(context_transitions[context])
            context_transitions[context][next_value] = 0
        end
        context_transitions[context][next_value] += 1
    end

    # Calculer l'entropie conditionnelle H(X|Context)
    total_transitions = sum(sum(values(transitions)) for transitions in values(context_transitions))

    if total_transitions == 0
        return 0.0
    end

    conditional_entropy = 0.0

    for (context, transitions) in context_transitions
        context_total = sum(values(transitions))
        context_prob = context_total / total_transitions

        # CORRECTION AEP: Entropie pour ce contexte spécifique selon AEP
        # Créer la séquence des valeurs suivantes pour ce contexte
        context_sequence = String[]
        for (next_value, count) in transitions
            append!(context_sequence, repeat([next_value], count))
        end

        # Appeler la méthode depuis l'analyzer
        if calculator.analyzer !== nothing
            context_entropy = _calculate_sequence_entropy_aep(calculator.analyzer, context_sequence)
        else
            # Fallback si pas d'analyzer (ne devrait pas arriver)
            context_entropy = 0.0
        end

        # Pondérer par la probabilité du contexte
        conditional_entropy += context_prob * context_entropy
    end

    return round(conditional_entropy, digits=4)
end