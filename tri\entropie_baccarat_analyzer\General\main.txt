# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3243 à 3412
# Type: Méthode

"""
    main()

🚀 POINT D'ENTRÉE PRINCIPAL - Interface utilisateur interactive

Fonction principale pour l'analyse d'entropie du baccarat.
Orchestre l'utilisation de toutes les classes du système pour
fournir une analyse entropique complète des parties de baccarat.

Référence: entropie/cours_entropie/niveau_debutant/02_formule_shannon.md
Application de la formule H(X) = -∑ p(x) log₂ p(x) au baccarat INDEX5
"""
function main()
    println("🎰 ANALYSEUR D'ENTROPIE BACCARAT - INDEX5")
    println("=" ^ 50)
    println("Basé sur les formules d'entropie de Shannon")
    println("Référence: entropie/cours_entropie/")
    println()

    # Initialisation de l'analyseur
    analyzer = BaccaratEntropyAnalyzer()

    # Chargement des données
    filepath = "partie/dataset_baccarat_lupasco_20250704_092825_condensed.json"
    data = load_baccarat_data(analyzer, filepath)

    if isempty(data)
        println("❌ Impossible de charger les données. Vérifiez le fichier.")
        return
    end

    println("📊 $(length(data)) parties chargées avec succès")
    println()

    # Menu interactif
    non_interactive_mode = false

    while true
        println("\n🎯 OPTIONS D'ANALYSE:")
        println("1. Analyser une partie spécifique")
        println("2. Analyser toutes les parties")
        println("3. Analyser les N premières parties")
        println("4. Afficher les statistiques théoriques")
        println("5. Quitter")

        choice = ""
        try
            print("\nChoisissez une option (1-5): ")
            choice = strip(readline())
        catch e
            if isa(e, EOFError)
                # Mode non-interactif : analyser la partie 1 par défaut
                println("Mode non-interactif détecté. Analyse de la partie 1...")
                choice = "1"
                non_interactive_mode = true
            else
                rethrow(e)
            end
        end

        if choice == "1"
            # Analyse d'une partie spécifique
            try
                game_index = 0  # Index 0-based
                if non_interactive_mode
                    game_index = 0  # Partie 1 par défaut
                else
                    try
                        print("Numéro de la partie (1-$(length(data))): ")
                        game_index = parse(Int, readline()) - 1
                    catch e
                        if isa(e, EOFError)
                            println("Mode non-interactif détecté. Analyse de la partie 1...")
                            game_index = 0
                            non_interactive_mode = true
                        else
                            rethrow(e)
                        end
                    end
                end

                if 0 <= game_index < length(data)
                    println("\n🔍 Analyse de la partie $(game_index + 1)...")

                    result = analyze_single_game(analyzer, data[game_index + 1], "Partie_$(game_index + 1)")

                    if haskey(result, "error")
                        println("❌ Erreur: $(result["error"])")
                    else
                        # Export automatique du rapport complet
                        rapport_filename = "rapport$(game_index + 1).txt"
                        rapport_complet = generate_entropy_report(analyzer, result)

                        try
                            open(rapport_filename, "w") do f
                                write(f, rapport_complet)
                            end
                            println("📄 Rapport exporté automatiquement : $rapport_filename")
                        catch e
                            println("❌ Erreur lors de l'export du rapport : $e")
                        end

                        # Proposition de visualisation (avec gestion EOFError)
                        try
                            print("\nAfficher le graphique d'évolution? (o/n): ")
                            show_plot = lowercase(strip(readline())) == "o"
                            if show_plot
                                # Génération automatique du nom de fichier JPG
                                jpg_filename = "graphique_entropie_partie_$(game_index + 1).jpg"
                                plot_entropy_evolution(analyzer, result, jpg_filename)
                            end

                            # Proposition d'export CSV
                            print("Exporter vers CSV? (o/n): ")
                            export_csv = lowercase(strip(readline())) == "o"
                            if export_csv
                                filename = "entropy_analysis_partie_$(game_index + 1).csv"
                                export_results_to_csv(analyzer, result, filename)
                            end
                        catch e
                            if isa(e, EOFError)
                                println("Mode non-interactif : pas de visualisation ni d'export")
                            else
                                rethrow(e)
                            end
                        end

                        # En mode non-interactif, sortir après l'analyse
                        if non_interactive_mode
                            println("\n✅ Analyse terminée en mode non-interactif")
                            return
                        end
                    end
                else
                    println("❌ Numéro de partie invalide")
                end
            catch e
                if isa(e, ArgumentError)
                    println("❌ Veuillez entrer un numéro valide")
                else
                    rethrow(e)
                end
            end

        elseif choice == "2"
            # Analyse de toutes les parties
            println("\n🔍 Analyse de toutes les parties...")
            try
                print("⚠️  Cela peut prendre du temps. Continuer? (o/n): ")
                confirm = lowercase(strip(readline()))
            catch e
                if isa(e, EOFError)
                    println("Mode non-interactif : analyse annulée")
                    return
                else
                    rethrow(e)
                end
            end

            if confirm == "o"
                global_stats = analyze_multiple_games(analyzer, data)

                if haskey(global_stats, "error")
                    println("❌ Erreur: $(global_stats["error"])")
                else
                    println("\n📊 STATISTIQUES GLOBALES ($(global_stats["total_games_analyzed"]) parties)")
                    println("=" ^ 60)
                    println("Entropie finale moyenne: $(round(global_stats["average_final_entropy"], digits=4)) ± $(round(global_stats["std_final_entropy"], digits=4)) bits")
                    println("Entropie finale min/max: $(round(global_stats["min_final_entropy"], digits=4)) / $(round(global_stats["max_final_entropy"], digits=4)) bits")
                    println("Longueur moyenne des parties: $(round(global_stats["average_sequence_length"], digits=1)) mains")
                    println("Position moyenne du max d'entropie: $(round(global_stats["average_max_entropy_position"], digits=1))")
                end
            end

        elseif choice == "3"
            # Analyse des N premières parties
            try
                print("Nombre de parties à analyser (max $(length(data))): ")
                n_games = parse(Int, readline())
                if 1 <= n_games <= length(data)
                    println("\n🔍 Analyse des $n_games premières parties...")

                    global_stats = analyze_multiple_games(analyzer, data[1:n_games])

                    if haskey(global_stats, "error")
                        println("❌ Erreur: $(global_stats["error"])")
                    else
                        println("\n📊 STATISTIQUES ($(global_stats["total_games_analyzed"]) parties)")
                        println("=" ^ 50)
                        println("Entropie finale moyenne: $(round(global_stats["average_final_entropy"], digits=4)) ± $(round(global_stats["std_final_entropy"], digits=4)) bits")
                        println("Entropie finale min/max: $(round(global_stats["min_final_entropy"], digits=4)) / $(round(global_stats["max_final_entropy"], digits=4)) bits")
                        println("Longueur moyenne des parties: $(round(global_stats["average_sequence_length"], digits=1)) mains")
                    end
                else
                    println("❌ Nombre invalide")
                end
            catch e
                if isa(e, ArgumentError)
                    println("❌ Veuillez entrer un nombre valide")
                elseif isa(e, EOFError)
                    println("Mode non-interactif : analyse annulée")
                    return
                else
                    rethrow(e)
                end
            end

        elseif choice == "4"
            # Statistiques théoriques
            println("\n📋 STATISTIQUES THÉORIQUES INDEX5")
            println("=" ^ 40)
            println("Nombre total de valeurs possibles: 18")
            println("Entropie théorique maximale: $(round(analyzer.theoretical_entropy, digits=4)) bits")
            println("Entropie uniforme (18 valeurs): $(round(log2(18), digits=4)) bits")
            println("\n🎯 Probabilités théoriques:")
            for (value, prob) in sort(collect(analyzer.theoretical_probs))
                println("  $value: $(round(prob, digits=4)) ($(round(prob*100, digits=2))%)")
            end

        elseif choice == "5"
            println("👋 Au revoir!")
            break

        else
            println("❌ Option invalide. Choisissez entre 1 et 5.")
        end
    end
end