# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1123 à 1146
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    export_results_to_csv(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any}, filename::String)

Exporte les résultats d'analyse vers un fichier CSV.

# Arguments
- `analysis_result::Dict{String, Any}`: Résultats d'analyse d'une partie
- `filename::String`: Nom du fichier CSV
"""
function export_results_to_csv(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any}, filename::String)
    if !haskey(analysis_result, "entropy_evolution")
        println("❌ Pas de données à exporter")
        return
    end

    evolution = analysis_result["entropy_evolution"]

    # Création du DataFrame (nécessite DataFrames.jl et CSV.jl)
    using DataFrames, CSV

    # Convertir les données en DataFrame
    df = DataFrame(evolution)

    # Ajout d'informations sur la partie
    df.game_id .= analysis_result["game_id"]
    df.theoretical_entropy_max .= analyzer.theoretical_entropy

    # Sauvegarde
    CSV.write(filename, df)
    println("📄 Résultats exportés vers: $filename")
end