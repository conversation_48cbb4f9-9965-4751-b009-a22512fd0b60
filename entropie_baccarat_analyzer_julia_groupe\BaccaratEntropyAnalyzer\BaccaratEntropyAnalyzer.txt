# ============================================================
# CLASSE: BaccaratEntropyAnalyzer
# Fichier consolidé généré automatiquement
# Nombre de fichiers sources: 22
# ============================================================

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                    CATÉGORIE 1 : STRUCTURE ET INITIALISATION                ║
# ║                           Lignes 1-66                                       ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    BaccaratEntropyAnalyzer(base::Float64=2.0, epsilon::Float64=1e-12)

🔧 INITIALISATION - Configuration du moteur d'analyse entropique

Initialise l'analyseur d'entropie pour le baccarat avec les probabilités
théoriques exactes INDEX5 et calcule l'entropie théorique maximale.

# Arguments
- `base::Float64`: Base du logarithme (2 pour bits, e pour nats)
- `epsilon::Float64`: Valeur minimale pour éviter log(0)
"""
mutable struct BaccaratEntropyAnalyzer
    base::Float64
    epsilon::Float64
    theoretical_probs::Dict{String, Float64}
    theoretical_entropy::Float64

    function BaccaratEntropyAnalyzer(base::Float64=2.0, epsilon::Float64=1e-12)
        # Probabilités théoriques INDEX5 EXACTES (en pourcentage)
        # CORRECTION EXPERTE: Utilisation des vraies probabilités calculées sur données réelles
        theoretical_probs = Dict{String, Float64}(
            "0_A_BANKER" => 8.5136, "1_A_BANKER" => 8.6389,
            "0_B_BANKER" => 6.4676, "1_B_BANKER" => 6.5479,  # CORRIGÉ: était 7.6907
            "0_C_BANKER" => 7.7903, "1_C_BANKER" => 7.8929,
            "0_A_PLAYER" => 8.5240, "1_A_PLAYER" => 8.6361,
            "0_B_PLAYER" => 7.6907, "1_B_PLAYER" => 7.7888,
            "0_C_PLAYER" => 5.9617, "1_C_PLAYER" => 6.0352,
            "0_A_TIE" => 1.7719, "1_A_TIE" => 1.7978,
            "0_B_TIE" => 1.6281, "1_B_TIE" => 1.6482,
            "0_C_TIE" => 1.3241, "1_C_TIE" => 1.3423
        )

        # Normalisation des probabilités
        total = sum(values(theoretical_probs))
        theoretical_probs = Dict(k => v/total for (k, v) in theoretical_probs)

        # Calcul de l'entropie théorique maximale
        theoretical_entropy = _calculate_shannon_entropy(collect(values(theoretical_probs)), base, epsilon)

        println("🎯 Entropie théorique INDEX5: $(round(theoretical_entropy, digits=4)) bits")

        new(base, epsilon, theoretical_probs, theoretical_entropy)
    end
end

"""
    _calculate_shannon_entropy(probabilities::Vector{Float64}, base::Float64, epsilon::Float64)

Calcule l'entropie de Shannon pour un vecteur de probabilités.
"""
function _calculate_shannon_entropy(probabilities::Vector{Float64}, base::Float64=2.0, epsilon::Float64=1e-12)
    entropy = 0.0
    for p in probabilities
        if p > epsilon
            entropy -= p * log(base, p)
        end
    end
    return entropy
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                CATÉGORIE 2 : ALGORITHMES D'ENTROPIE AVANCÉE                  ║
# ║                           Lignes 67-168                                      ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    _approximate_lz_complexity(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Int

Approximation de la complexité de Lempel-Ziv.

Référence: entropie/cours_entropie/niveau_intermediaire/02_codage_source.md
"""
function _approximate_lz_complexity(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Int
    if isempty(sequence)
        return 0
    end

    dictionary = Set{Tuple}()
    i = 1
    complexity = 0

    while i <= length(sequence)
        # Chercher le plus long préfixe non vu
        found = false
        for length in 1:(length(sequence) - i + 1)
            substring = Tuple(sequence[i:i+length-1])
            if substring ∉ dictionary
                push!(dictionary, substring)
                complexity += 1
                i += length
                found = true
                break
            end
        end

        if !found
            # Tous les préfixes sont dans le dictionnaire
            i += 1
            complexity += 1
        end
    end

    return complexity
end


"""
    _approximate_topological_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64

Approximation de l'entropie topologique respectant le principe variationnel.
CORRECTION EXPERTE: Assure h_top ≥ h_μ selon le principe variationnel.

Référence: entropie/cours_entropie/niveau_expert/02_entropie_topologique.md
Principe: h_top(T) = sup{h_μ(T) : μ T-invariante} ≥ h_μ(T)
"""
function _approximate_topological_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end

    # Compter les motifs uniques de longueurs croissantes
    max_length = min(5, length(sequence))
    pattern_counts = Int[]

    for length in 1:max_length
        patterns = Set{Tuple}()
        for i in 1:(length(sequence) - length + 1)
            pattern = Tuple(sequence[i:i+length-1])
            push!(patterns, pattern)
        end
        push!(pattern_counts, length(patterns))
    end

    if length(pattern_counts) < 2
        return 0.0
    end

    # CORRECTION EXPERTE: Estimation correcte respectant le principe variationnel
    # h_top = lim_{n→∞} (1/n) log(N(n)) où N(n) = nombre de motifs de longueur n
    # Formule correcte: (1/n) log(N(n))
    growth_rates = Float64[]
    for i in 1:length(pattern_counts)
        length_val = i  # Longueur du motif (commence à 1)
        if pattern_counts[i] > 0
            growth_rate = _safe_log(analyzer, [pattern_counts[i]])[1] / length_val
            push!(growth_rates, growth_rate)
        end
    end

    if !isempty(growth_rates)
        # Prendre le maximum pour respecter le principe variationnel
        h_top_estimate = maximum(growth_rates)
    else
        h_top_estimate = _safe_log(analyzer, [pattern_counts[end]])[1] / max_length
    end

    # CORRECTION: Assurer h_top ≥ h_μ (principe variationnel)
    # Calculer une estimation rapide de h_μ pour comparaison
    if length(sequence) >= 3
        h_metric_estimate = _estimate_metric_entropy(analyzer, sequence, 3)
        h_top_estimate = max(h_top_estimate, h_metric_estimate * 1.1)  # Marge de sécurité
    end

    return h_top_estimate
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                CATÉGORIE 3 : CALCULS D'ENTROPIE PAR BLOCS                    ║
# ║                           Lignes 169-261                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    _calculate_block_entropies(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Vector{Float64}

Calcule l'entropie pour des blocs de différentes longueurs.
CORRECTION: Utilise les probabilités théoriques pour les blocs de longueur 1

Référence: entropie/cours_entropie/ressources/implementations_python.py
Méthode de l'entropie métrique par blocs.
"""
function _calculate_block_entropies(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Vector{Float64}
    if length(sequence) < 2
        return [0.0]
    end

    entropies = Float64[]

    for block_len in 1:min(max_length, length(sequence))
        if block_len == 1
            # CORRECTION AEP: Pour les blocs de longueur 1, calculer l'entropie de la séquence complète
            # selon la formule AEP au lieu d'utiliser toutes les probabilités théoriques
            block_entropy = _calculate_sequence_entropy_aep(analyzer, sequence)
            push!(entropies, block_entropy)
        else
            # Pour les blocs de longueur > 1, créer des sous-séquences et utiliser AEP
            block_sequences = Vector{String}[]
            for i in 1:(length(sequence) - block_len + 1)
                block_sequence = sequence[i:i+block_len-1]
                push!(block_sequences, block_sequence)
            end

            if isempty(block_sequences)
                push!(entropies, 0.0)
                continue
            end

            # CORRECTION AEP: Calculer l'entropie moyenne des blocs selon AEP
            total_entropy = 0.0
            for block_seq in block_sequences
                total_entropy += _calculate_sequence_entropy_aep(analyzer, block_seq)
            end

            block_entropy = !isempty(block_sequences) ? total_entropy / length(block_sequences) : 0.0
            push!(entropies, block_entropy)
        end
    end

    return entropies
end


"""
    _calculate_block_entropies_raw(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Vector{Float64}

Calcule l'entropie pour des blocs de différentes longueurs SANS normalisation.
NOUVELLE MÉTHODE: Pour le calcul correct de l'entropie métrique.

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
"""
function _calculate_block_entropies_raw(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Vector{Float64}
    if length(sequence) < 2
        return [0.0]
    end

    entropies = Float64[]

    for block_len in 1:min(max_length, length(sequence))
        # CORRECTION AEP: Créer toutes les sous-séquences de cette longueur
        block_sequences = Vector{String}[]
        for i in 1:(length(sequence) - block_len + 1)
            block_sequence = sequence[i:i+block_len-1]
            push!(block_sequences, block_sequence)
        end

        if isempty(block_sequences)
            push!(entropies, 0.0)
            continue
        end

        # CORRECTION AEP: Calculer l'entropie moyenne des blocs selon la formule AEP
        total_entropy = 0.0
        for block_seq in block_sequences
            total_entropy += _calculate_sequence_entropy_aep(analyzer, block_seq)
        end

        # Entropie moyenne des blocs de cette longueur
        block_entropy = !isempty(block_sequences) ? total_entropy / length(block_sequences) : 0.0
        push!(entropies, block_entropy)
    end

    return entropies
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║            CATÉGORIE 4 : ENTROPIES CONDITIONNELLES ET MÉTRIQUES              ║
# ║                           Lignes 262-518                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    _calculate_conditional_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64

Calcule l'entropie conditionnelle H(Xₙ|X₁,...,Xₙ₋₁) avec contexte fixe.
CORRECTION EXPERTE: Utilise un contexte de longueur fixe pour cohérence mathématique.

Référence: entropie/cours_entropie/niveau_debutant/03_entropie_conditionnelle.md
Formule: H(X|Y) = ∑ P(y) × H(X|y)
"""
function _calculate_conditional_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end

    # CORRECTION: Utiliser un contexte de longueur fixe (longueur 1 pour simplicité)
    # Cela donne H(Xₙ|Xₙ₋₁) au lieu de mélanger différentes longueurs
    context_length = min(1, length(sequence) - 1)

    # Compter les transitions contexte → symbole suivant
    context_transitions = Dict{Any, Dict{String, Int}}()

    for i in 1:(length(sequence) - context_length)
        if context_length == 1
            context = sequence[i]  # Contexte de longueur 1
        else
            context = Tuple(sequence[i:i+context_length-1])
        end

        next_symbol = sequence[i+context_length]

        if context ∉ keys(context_transitions)
            context_transitions[context] = Dict{String, Int}()
        end

        if next_symbol ∉ keys(context_transitions[context])
            context_transitions[context][next_symbol] = 0
        end
        context_transitions[context][next_symbol] += 1
    end

    if isempty(context_transitions)
        return 0.0
    end

    # Calculer H(X|Contexte) = ∑ P(contexte) × H(X|contexte)
    total_transitions = sum(sum(values(transitions)) for transitions in values(context_transitions))
    conditional_entropy = 0.0

    for (context, transitions) in context_transitions
        context_prob = sum(values(transitions)) / total_transitions

        # CORRECTION AEP: H(X|ce contexte) calculé selon AEP
        # Créer la séquence des symboles suivants pour ce contexte
        context_sequence = String[]
        for (next_symbol, count) in transitions
            append!(context_sequence, repeat([next_symbol], count))
        end

        context_entropy = _calculate_sequence_entropy_aep(analyzer, context_sequence)
        conditional_entropy += context_prob * context_entropy
    end

    return conditional_entropy
end


"""
    _calculate_repetition_rate(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64

Calcule le taux de répétition dans la séquence.
"""
function _calculate_repetition_rate(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end

    # Compter les répétitions immédiates
    repetitions = 0
    for i in 1:(length(sequence) - 1)
        if sequence[i] == sequence[i + 1]
            repetitions += 1
        end
    end

    return repetitions / (length(sequence) - 1)
end


"""
    _calculate_sequence_complexity(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Dict{String, Any}

Calcule diverses métriques de complexité de la séquence.

Référence: entropie/cours_entropie/niveau_expert/02_entropie_topologique.md
"""
function _calculate_sequence_complexity(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Dict{String, Any}
    n = length(sequence)

    # Nombre de motifs uniques de différentes longueurs
    unique_patterns = Dict{String, Int}()
    for length in 1:min(5, n)
        patterns = Set{Tuple}()
        for i in 1:(n - length + 1)
            pattern = Tuple(sequence[i:i+length-1])
            push!(patterns, pattern)
        end
        unique_patterns["length_$length"] = length(patterns)
    end

    # Complexité de Lempel-Ziv (approximation)
    lz_complexity = _approximate_lz_complexity(analyzer, sequence)

    # Entropie topologique approximée
    topological_entropy = _approximate_topological_entropy(analyzer, sequence)

    return Dict{String, Any}(
        "unique_patterns" => unique_patterns,
        "lz_complexity" => lz_complexity,
        "topological_entropy" => topological_entropy,
        "sequence_diversity" => length(Set(sequence)) / length(analyzer.theoretical_probs),  # Diversité relative
        "repetition_rate" => _calculate_repetition_rate(analyzer, sequence)
    )
end


"""
    _calculate_sequence_entropy_aep(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64

Calcule l'entropie d'une séquence selon la formule AEP exacte :
H_séquence = -(1/n) × log₂ p(X₁, X₂, ..., Xₙ)
où p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ) pour séquences indépendantes

Référence: Elements of Information Theory - ligne 1919
"The AEP states that (1/n) log (1/p(X₁, X₂, ..., Xₙ)) is close to the entropy H"

Cette formule unifiée remplace tous les calculs d'entropie de séquence
pour garantir la cohérence mathématique selon la théorie de l'information.

# Arguments
- `sequence::Vector{String}`: Liste des valeurs INDEX5 dans la séquence

# Returns
- `Float64`: Entropie de la séquence selon AEP en bits
"""
function _calculate_sequence_entropy_aep(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64
    if isempty(sequence)
        return 0.0
    end

    # Calculer -log₂ de la probabilité jointe théorique
    # p(séquence) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)
    # log₂(p(séquence)) = ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
    total_log_prob = 0.0
    for value in sequence
        if haskey(analyzer.theoretical_probs, value)
            p_theo = analyzer.theoretical_probs[value]
            if p_theo > 0
                total_log_prob += log2(p_theo)
            else
                # Si probabilité théorique = 0, utiliser une valeur très faible
                total_log_prob += log2(analyzer.epsilon)
            end
        else
            # Si valeur non trouvée dans les probabilités théoriques, utiliser une probabilité très faible
            total_log_prob += log2(analyzer.epsilon)
        end
    end

    # Retourner l'entropie de la séquence : -(1/n) × ∑log₂(p_théo(xᵢ))
    return -total_log_prob / length(sequence)
end


"""
    _calculate_shannon_entropy(analyzer::BaccaratEntropyAnalyzer, probabilities::Vector{Float64})::Float64

📊 SHANNON - Calcule l'entropie de Shannon: H(X) = -∑ p(x) log₂ p(x)

Formule fondamentale de la théorie de l'information pour mesurer
l'incertitude d'une distribution de probabilités.

Référence: entropie/cours_entropie/niveau_debutant/02_formule_shannon.md

# Arguments
- `probabilities::Vector{Float64}`: Liste des probabilités

# Returns
- `Float64`: Entropie de Shannon en bits
"""
function _calculate_shannon_entropy(analyzer::BaccaratEntropyAnalyzer, probabilities::Vector{Float64})::Float64
    p = _validate_probabilities(analyzer, probabilities)

    # Calcul avec gestion de 0*log(0) = 0
    log_p = _safe_log(analyzer, p)
    entropy_terms = p .* log_p

    # Remplace NaN par 0 (cas 0*log(0))
    entropy_terms = [p[i] == 0 ? 0.0 : entropy_terms[i] for i in 1:length(p)]

    return -sum(entropy_terms)
end


"""
    _calculate_simple_entropy_theoretical(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64

Calcule l'entropie théorique simple - WRAPPER vers la méthode AEP unifiée.
Maintenu pour compatibilité avec le code existant.
"""
function _calculate_simple_entropy_theoretical(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64
    return _calculate_sequence_entropy_aep(analyzer, sequence)
end


"""
    _estimate_metric_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Float64

Estime l'entropie métrique h_μ(T) selon Kolmogorov-Sinai.
CORRECTION EXPERTE: Calcul rigoureux de la limite h_μ(T) = lim H(n)/n.

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
Formule: h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
"""
function _estimate_metric_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Float64
    if length(sequence) < 3
        return 0.0
    end

    # CORRECTION: Calculer les entropies de blocs sans normalisation par longueur
    block_entropies_raw = _calculate_block_entropies_raw(analyzer, sequence, max_length)

    if length(block_entropies_raw) < 2
        return 0.0
    end

    # CORRECTION EXPERTE: Calcul rigoureux de h_μ = lim_{n→∞} H(n)/n
    # Formule correcte: h_μ(T) = lim H(n)/n selon Kolmogorov-Sinai
    h_metric_estimates = Float64[]
    for (i, entropy) in enumerate(block_entropies_raw)
        block_length = i  # Longueur du bloc (commence à 1)
        if block_length > 0
            h_estimate = entropy / block_length  # H(n)/n
            push!(h_metric_estimates, h_estimate)
        end
    end

    # Prendre la dernière estimation comme approximation de la limite
    if !isempty(h_metric_estimates)
        h_metric = max(0.0, h_metric_estimates[end])
    else
        h_metric = 0.0
    end

    return h_metric
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                    CATÉGORIE 5 : FONCTIONS UTILITAIRES                       ║
# ║                           Lignes 519-564                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    _safe_log(analyzer::BaccaratEntropyAnalyzer, x::Vector{Float64})::Vector{Float64}

🔒 SÉCURITÉ - Calcul sécurisé du logarithme avec gestion de log(0)

Évite les erreurs mathématiques en remplaçant les valeurs nulles ou négatives
par epsilon avant le calcul logarithmique.

Référence: entropie/cours_entropie/ressources/implementations_python.py
"""
function _safe_log(analyzer::BaccaratEntropyAnalyzer, x::Vector{Float64})::Vector{Float64}
    x_safe = [val <= 0 ? analyzer.epsilon : val for val in x]
    return [log(analyzer.base, val) for val in x_safe]
end


"""
    _validate_probabilities(analyzer::BaccaratEntropyAnalyzer, p::Vector{Float64})::Vector{Float64}

✅ VALIDATION - Valide et normalise un vecteur de probabilités

Assure que les probabilités sont positives et normalisées (somme = 1).
Applique une distribution uniforme si toutes les probabilités sont nulles.

Référence: entropie/cours_entropie/ressources/implementations_python.py
"""
function _validate_probabilities(analyzer::BaccaratEntropyAnalyzer, p::Vector{Float64})::Vector{Float64}
    p = Float64.(p)

    if any(p .< 0)
        throw(ArgumentError("Les probabilités doivent être positives"))
    end

    total = sum(p)
    if total > 0
        p = p ./ total
    else
        # Distribution uniforme si toutes les probabilités sont nulles
        p = ones(length(p)) ./ length(p)
    end

    return p
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                     CATÉGORIE 6 : ANALYSE DE PARTIES                         ║
# ║                           Lignes 565-766                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    analyze_multiple_games(analyzer::BaccaratEntropyAnalyzer, data::Vector{Dict{String, Any}}, max_games::Union{Int, Nothing}=nothing)::Dict{String, Any}

Analyse multiple parties et calcule des statistiques globales.

# Arguments
- `data::Vector{Dict{String, Any}}`: Liste des données de parties
- `max_games::Union{Int, Nothing}`: Nombre maximum de parties à analyser (optionnel)

# Returns
- `Dict{String, Any}`: Statistiques globales d'analyse
"""
function analyze_multiple_games(analyzer::BaccaratEntropyAnalyzer, data::Vector{Dict{String, Any}}, max_games::Union{Int, Nothing}=nothing)::Dict{String, Any}
    if max_games !== nothing
        data = data[1:min(max_games, length(data))]
    end

    all_results = Dict{String, Any}[]
    successful_analyses = 0

    println("🔄 Analyse de $(length(data)) parties...")

    for (i, game_data) in enumerate(data)
        game_id = "Game_$i"
        result = analyze_single_game(analyzer, game_data, game_id)

        if !haskey(result, "error")
            push!(all_results, result)
            successful_analyses += 1
        end

        if i % 10 == 0
            println("   Progression: $i/$(length(data)) parties analysées")
        end
    end

    if isempty(all_results)
        return Dict{String, Any}("error" => "Aucune partie analysée avec succès")
    end

    # Calcul des statistiques globales
    final_entropies = [result["final_metric_entropy"] for result in all_results]
    sequence_lengths = [result["sequence_length"] for result in all_results]
    max_entropy_positions = [result["max_metric_entropy_position"] for result in all_results]

    return Dict{String, Any}(
        "total_games_analyzed" => successful_analyses,
        "average_final_entropy" => mean(final_entropies),
        "std_final_entropy" => std(final_entropies),
        "min_final_entropy" => minimum(final_entropies),
        "max_final_entropy" => maximum(final_entropies),
        "average_sequence_length" => mean(sequence_lengths),
        "average_max_entropy_position" => mean(max_entropy_positions),
        "all_results" => all_results
    )
end


"""
    analyze_single_game(analyzer::BaccaratEntropyAnalyzer, game_data::Dict{String, Any}, game_id::Union{String, Nothing}=nothing)::Dict{String, Any}

Analyse complète d'une seule partie selon les méthodes avancées d'entropie.

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md

# Arguments
- `game_data::Dict{String, Any}`: Données de la partie
- `game_id::Union{String, Nothing}`: Identifiant de la partie (optionnel)

# Returns
- `Dict{String, Any}`: Résultats d'analyse complets avec entropie métrique
"""
function analyze_single_game(analyzer::BaccaratEntropyAnalyzer, game_data::Dict{String, Any}, game_id::Union{String, Nothing}=nothing)::Dict{String, Any}
    sequence = extract_index5_sequence(analyzer, game_data)

    if isempty(sequence)
        return Dict{String, Any}("error" => "Aucune séquence INDEX5 trouvée")
    end

    # Nouvelle méthode : entropie de blocs et métrique
    entropy_evolution = calculate_block_entropy_evolution(analyzer, sequence, max_block_length=4)

    if isempty(entropy_evolution)
        return Dict{String, Any}("error" => "Impossible de calculer l'évolution d'entropie")
    end

    # Extraction des métriques finales
    final_analysis = entropy_evolution[end]

    # Calcul de la complexité de la séquence
    complexity_metrics = _calculate_sequence_complexity(analyzer, sequence)

    # Positions d'intérêt
    max_metric_entropy_position = 0
    max_conditional_entropy_position = 0

    if !isempty(entropy_evolution)
        max_metric_entropy_position = argmax([x["metric_entropy"] for x in entropy_evolution])
        max_conditional_entropy_position = argmax([x["conditional_entropy"] for x in entropy_evolution])
        max_metric_entropy_position = entropy_evolution[max_metric_entropy_position]["position"]
        max_conditional_entropy_position = entropy_evolution[max_conditional_entropy_position]["position"]
    end

    return Dict{String, Any}(
        "game_id" => game_id !== nothing ? game_id : "Unknown",
        "sequence_length" => length(sequence),
        "full_sequence" => sequence,
        "entropy_evolution" => entropy_evolution,

        # Métriques finales (nouvelle approche)
        "final_metric_entropy" => final_analysis["metric_entropy"],
        "final_conditional_entropy" => final_analysis["conditional_entropy"],
        "final_entropy_rate" => final_analysis["entropy_rate"],
        "final_simple_entropy" => final_analysis["simple_entropy"],  # Ancienne méthode

        # Analyse de complexité
        "complexity_metrics" => complexity_metrics,

        # Positions d'intérêt
        "max_metric_entropy_position" => max_metric_entropy_position,
        "max_conditional_entropy_position" => max_conditional_entropy_position
    )
end


"""
    calculate_block_entropy_evolution(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_block_length::Int=5)::Vector{Dict{String, Any}}

📈 ÉVOLUTION - Calcule l'évolution de l'entropie par blocs (Kolmogorov-Sinai)

Méthode centrale d'analyse qui calcule position par position :
- Entropie de Shannon observée et théorique
- Entropie conditionnelle H(Xₙ|Xₙ₋₁)
- Entropie métrique h_μ(T)
- Taux d'entropie asymptotique

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
Méthode: entropie/cours_entropie/ressources/implementations_python.py (metric_entropy_estimate)

# Arguments
- `sequence::Vector{String}`: Séquence des valeurs INDEX5
- `max_block_length::Int`: Longueur maximale des blocs à analyser

# Returns
- `Vector{Dict{String, Any}}`: Liste des résultats d'entropie pour chaque position
"""
function calculate_block_entropy_evolution(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_block_length::Int=5)::Vector{Dict{String, Any}}
    results = Dict{String, Any}[]

    for n in 1:length(sequence)
        # Sous-séquence de longueur n
        subsequence = sequence[1:n]

        # Calcul de l'entropie de blocs pour différentes longueurs
        block_entropies = _calculate_block_entropies(analyzer, subsequence, max_block_length)

        # Entropie conditionnelle (prédictibilité du prochain symbole)
        conditional_entropy = _calculate_conditional_entropy(analyzer, subsequence)

        # Estimation de l'entropie métrique (taux de création d'information)
        metric_entropy = _estimate_metric_entropy(analyzer, subsequence, max_block_length)

        # Comptage simple pour comparaison (fréquences observées)
        counts = Dict{String, Int}()
        for value in subsequence
            counts[value] = get(counts, value, 0) + 1
        end
        total = length(subsequence)
        empirical_probs = [counts[value] / total for value in keys(counts)]

        # CORRECTION: Entropie simple observée basée sur fréquences empiriques
        # Calcul de l'entropie de Shannon sur les fréquences observées dans la sous-séquence
        simple_entropy_observed = _calculate_shannon_entropy(analyzer, empirical_probs)

        # CORRECTION: Entropie simple avec probabilités théoriques (formule AEP)
        simple_entropy_theoretical = _calculate_sequence_entropy_aep(analyzer, subsequence)

        # Créer les dictionnaires pour les probabilités empiriques
        empirical_probabilities = Dict{String, Float64}()
        for (value, count) in counts
            empirical_probabilities[value] = count / total
        end

        push!(results, Dict{String, Any}(
            "position" => n,
            "sequence_length" => n,
            "unique_values" => length(counts),
            "simple_entropy" => simple_entropy_observed,  # Entropie de Shannon sur fréquences observées
            "simple_entropy_theoretical" => simple_entropy_theoretical,  # Entropie AEP avec probabilités théoriques
            "block_entropies" => block_entropies,  # H(blocs de longueur k)
            "conditional_entropy" => conditional_entropy,  # H(Xₙ|X₁,...,Xₙ₋₁)
            "metric_entropy" => metric_entropy,  # Estimation h_μ(T)
            "entropy_rate" => !isempty(block_entropies) ? block_entropies[end] : 0.0,  # Taux d'entropie
            "observed_values" => collect(keys(counts)),
            "counts" => counts,
            "empirical_probabilities" => empirical_probabilities
        ))
    end

    return results
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                   CATÉGORIE 7 : EXPORT ET EXTRACTION                         ║
# ║                           Lignes 767-851                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    export_results_to_csv(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any}, filename::String)

Exporte les résultats d'analyse vers un fichier CSV.

# Arguments
- `analysis_result::Dict{String, Any}`: Résultats d'analyse d'une partie
- `filename::String`: Nom du fichier CSV
"""
function export_results_to_csv(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any}, filename::String)
    if !haskey(analysis_result, "entropy_evolution")
        println("❌ Pas de données à exporter")
        return
    end

    evolution = analysis_result["entropy_evolution"]

    # Création du DataFrame (nécessite DataFrames.jl et CSV.jl)
    using DataFrames, CSV

    # Convertir les données en DataFrame
    df = DataFrame(evolution)

    # Ajout d'informations sur la partie
    df.game_id .= analysis_result["game_id"]
    df.theoretical_entropy_max .= analyzer.theoretical_entropy

    # Sauvegarde
    CSV.write(filename, df)
    println("📄 Résultats exportés vers: $filename")
end


"""
    extract_index5_sequence(analyzer::BaccaratEntropyAnalyzer, game_data::Dict{String, Any})::Vector{String}

🔍 EXTRACTION - Extrait la séquence INDEX5 d'une partie

Filtre les mains d'ajustement et extrait uniquement les valeurs INDEX5 valides
pour l'analyse entropique.

# Arguments
- `game_data::Dict{String, Any}`: Données d'une partie

# Returns
- `Vector{String}`: Séquence des valeurs INDEX5 (sans les mains d'ajustement)
"""
function extract_index5_sequence(analyzer::BaccaratEntropyAnalyzer, game_data::Dict{String, Any})::Vector{String}
    sequence = String[]

    # Vérifier différentes structures possibles
    if haskey(game_data, "hands")
        # Structure: {"hands": [...]}
        for hand in game_data["hands"]
            # Exclure les mains d'ajustement (main_number null ou INDEX5 vide)
            if (haskey(hand, "main_number") && hand["main_number"] !== nothing &&
                haskey(hand, "INDEX5") &&
                hand["INDEX5"] !== nothing &&
                !isempty(strip(hand["INDEX5"])))
                push!(sequence, hand["INDEX5"])
            end
        end

    elseif haskey(game_data, "mains_condensees")
        # Structure: {"mains_condensees": [...]}
        for main in game_data["mains_condensees"]
            # Exclure les mains d'ajustement (main_number null ou index5 vide)
            if (haskey(main, "main_number") && main["main_number"] !== nothing &&
                haskey(main, "index5") &&
                main["index5"] !== nothing &&
                !isempty(strip(main["index5"])))
                push!(sequence, main["index5"])
            end
        end
    else
        println("❌ Structure de partie non reconnue. Clés disponibles: $(collect(keys(game_data)))")
        return String[]
    end

    println("🔍 Séquence extraite: $(length(sequence)) mains valides (mains d'ajustement exclues)")

    return sequence
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                  CATÉGORIE 8 : GÉNÉRATION DE RAPPORTS                        ║
# ║                          Lignes 852-1191                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    generate_entropy_report(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any})::String

Génère un rapport détaillé d'analyse d'entropie selon les méthodes avancées.

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md

# Arguments
- `analysis_result::Dict{String, Any}`: Résultats d'analyse d'une partie

# Returns
- `String`: Rapport formaté en texte avec métriques avancées
"""
function generate_entropy_report(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any})::String
    if !haskey(analysis_result, "entropy_evolution")
        return "❌ Pas de données d'analyse disponibles"
    end

    evolution = analysis_result["entropy_evolution"]
    game_id = analysis_result["game_id"]
    sequence_length = analysis_result["sequence_length"]
    complexity = get(analysis_result, "complexity_metrics", Dict{String, Any}())

    # Statistiques des nouvelles métriques
    metric_entropies = [item["metric_entropy"] for item in evolution]
    conditional_entropies = [item["conditional_entropy"] for item in evolution]
    entropy_rates = [item["entropy_rate"] for item in evolution]
    simple_entropies = [item["simple_entropy"] for item in evolution]  # Ancienne méthode

    # Valeurs finales
    final_metric = get(analysis_result, "final_metric_entropy", 0)
    final_conditional = get(analysis_result, "final_conditional_entropy", 0)
    final_rate = get(analysis_result, "final_entropy_rate", 0)
    final_simple = get(analysis_result, "final_simple_entropy", 0)

    # Positions des maxima
    max_metric_pos = get(analysis_result, "max_metric_entropy_position", 0)
    max_conditional_pos = get(analysis_result, "max_conditional_entropy_position", 0)

    report = """
🎯 RAPPORT D'ANALYSE D'ENTROPIE AVANCÉE - INDEX5
===============================================
Méthodes: Kolmogorov-Sinai, Entropie de Blocs, Entropie Conditionnelle
Référence: entropie/cours_entropie/niveau_expert/

📊 INFORMATIONS GÉNÉRALES
Partie ID: $game_id
Longueur de la séquence: $sequence_length mains
Entropie théorique maximale: $(round(analyzer.theoretical_entropy, digits=4)) bits

📈 MÉTRIQUES D'ENTROPIE AVANCÉES
┌─ Entropie Métrique (Kolmogorov-Sinai) ─┐
│ Finale: $(round(final_metric, digits=4)) bits/symbole │
│ Maximum: $(round(maximum(metric_entropies), digits=4)) bits (position $max_metric_pos) │
│ Moyenne: $(round(mean(metric_entropies), digits=4)) bits │
└────────────────────────────────────────┘

┌─ Entropie Conditionnelle H(Xₙ|X₁...Xₙ₋₁) ─┐
│ Finale: $(round(final_conditional, digits=4)) bits │
│ Maximum: $(round(maximum(conditional_entropies), digits=4)) bits (position $max_conditional_pos) │
│ Moyenne: $(round(mean(conditional_entropies), digits=4)) bits │
│ → Mesure la prédictibilité du prochain symbole │
└─────────────────────────────────────────────┘

┌─ Taux d'Entropie (Entropy Rate) ─┐
│ Final: $(round(final_rate, digits=4)) bits/symbole │
│ → Limite asymptotique de l'information par symbole │
└──────────────────────────────────┘

📊 COMPARAISON DES MÉTHODES D'ENTROPIE
Entropie simple (fréquences observées): $(round(final_simple, digits=4)) bits
Entropie simple (probabilités théoriques): $(round(get(evolution[end], "simple_entropy_theoretical", 0), digits=4)) bits
Entropie métrique (Kolmogorov-Sinai): $(round(final_metric, digits=4)) bits
Différence observée vs métrique: $(round(abs(final_simple - final_metric), digits=4)) bits
Différence théorique vs métrique: $(round(abs(get(evolution[end], "simple_entropy_theoretical", 0) - final_metric), digits=4)) bits

🔬 ANALYSE DE COMPLEXITÉ
"""

    # Ajout des métriques de complexité
    if !isempty(complexity)
        report *= """Complexité Lempel-Ziv: $(get(complexity, "lz_complexity", "N/A"))
Entropie Topologique: $(round(get(complexity, "topological_entropy", 0), digits=4)) bits
Diversité relative: $(round(get(complexity, "sequence_diversity", 0)*100, digits=1))%
Taux de répétition: $(round(get(complexity, "repetition_rate", 0)*100, digits=1))%

🎲 MOTIFS UNIQUES OBSERVÉS
"""
        unique_patterns = get(complexity, "unique_patterns", Dict{String, Any}())
        for (length, count) in unique_patterns
            length_num = split(length, "_")[2]
            report *= "Longueur $length_num: $count motifs uniques\n"
        end
    end

    # Initialiser le calculateur, prédicteur, validateur et analyseur différentiel INDEX5
    # CORRECTION: Passer l'instance analyzer pour accéder à _calculate_sequence_entropy_aep
    calculator = INDEX5Calculator(analyzer)
    predictor = INDEX5Predictor()
    validator = INDEX5PredictionValidator()
    differential_analyzer = INDEX5DifferentialAnalyzer()

    # Calculer les différentiels
    differentials = calculate_differentials(differential_analyzer, evolution)

    report *= """
📋 ÉVOLUTION COMPLÈTE - TOUTES LES $(length(evolution)) MAINS AVEC MÉTRIQUES ET PRÉDICTIONS INDEX5
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | Uniques | Métriques INDEX5 | Prédiction POUR cette main
---------|-------------|----------|----------------|------|-------------|--------------|---------|------------------|---------------------------
"""

    # Pré-calculer toutes les prédictions pour éviter le décalage
    sequence = get(analysis_result, "full_sequence", String[])
    predictions = ["N/A"]  # Première main n'a pas de prédiction

    for i in 1:(length(evolution) - 1)
        item = evolution[i]
        sequence_up_to_i = i <= length(sequence) ? sequence[1:i] : sequence
        current_metrics = Dict{String, Any}(
            "conditional_entropy" => get(item, "conditional_entropy", 0),
            "metric_entropy" => get(item, "metric_entropy", 0),
            "repetition_rate" => get(item, "entropy_rate", 0),
            "predictability_score" => 1 - (get(item, "conditional_entropy", 3.9309) / 3.9309)
        )

        # Calculer la prédiction pour la main suivante (i+1)
        if length(sequence_up_to_i) >= 5  # Minimum requis pour prédiction
            prediction_result = predict_next_index5(predictor, sequence_up_to_i, current_metrics)
            if prediction_result !== nothing && isa(prediction_result, Dict)
                predicted_value = get(prediction_result, "predicted_index5", "N/A")
                confidence = get(prediction_result, "confidence", 0)

                # Gestion spéciale pour WAIT
                if predicted_value == "WAIT"
                    prediction_display = "WAIT"
                else
                    prediction_display = "$predicted_value($(round(confidence, digits=2)))"
                end
            else
                # Essayer prédiction contextuelle simple
                simple_pred = predict_context_level(predictor, sequence_up_to_i, current_metrics)
                prediction_display = simple_pred !== nothing ? simple_pred : "N/A"
            end
        else
            prediction_display = "N/A"
        end

        push!(predictions, prediction_display)

        # Valider la prédiction avec la valeur réelle suivante
        if prediction_display != "N/A"
            next_actual_index5 = sequence[i + 1]
            validate_prediction(validator, prediction_display, next_actual_index5, item["position"] + 1)
        end
    end

    # Générer le rapport avec les prédictions correctement alignées
    for (i, item) in enumerate(evolution)
        simple_theo = get(item, "simple_entropy_theoretical", 0)
        index5_value = i <= length(sequence) ? sequence[i] : "N/A"

        # Calculer les métriques INDEX5 pour cette position
        sequence_up_to_i = i <= length(sequence) ? sequence[1:i] : sequence
        current_metrics = Dict{String, Any}(
            "conditional_entropy" => get(item, "conditional_entropy", 0),
            "metric_entropy" => get(item, "metric_entropy", 0),
            "repetition_rate" => get(item, "entropy_rate", 0),
            "predictability_score" => 1 - (get(item, "conditional_entropy", 3.9309) / 3.9309)
        )

        # Calculer les nouvelles métriques INDEX5
        if length(sequence_up_to_i) >= 2
            index5_metrics = calculate_all_metrics(calculator, sequence_up_to_i, current_metrics, evolution[1:i])

            # Sélectionner les 3 métriques les plus importantes pour l'affichage
            context_pred = get(index5_metrics, "context_predictability", 0)
            pattern_str = get(index5_metrics, "pattern_strength", 0)
            consensus = get(index5_metrics, "multi_algorithm_consensus", 0)

            metrics_display = @sprintf("Ctx:%.3f Pat:%.3f Con:%.3f", context_pred, pattern_str, consensus)
        else
            metrics_display = "Ctx:0.000 Pat:0.000 Con:0.000"
        end

        # Utiliser la prédiction pré-calculée pour cette main
        prediction_display = i <= length(predictions) ? predictions[i] : "N/A"

        report *= @sprintf("Main %2d  | %11s | %6.3f  | %12.3f  | %4.3f | %9.3f | %10.3f | %2d/18 | %s | %14s\n",
            item["position"], index5_value, item["metric_entropy"], item["conditional_entropy"],
            item["entropy_rate"], item["simple_entropy"], simple_theo, item["unique_values"],
            metrics_display, prediction_display)
    end

    # Ajouter le nouveau tableau avec les différentiels
    report *= """

📊 TABLEAU AVEC DIFFÉRENTIELS - ANALYSE DES VARIATIONS ENTRE MAINS
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | DiffCond | DiffTaux | DiffDivEntropG | DiffEntropG | Prédiction
---------|-------------|----------|----------------|------|-------------|--------------|----------|----------|----------------|-------------|------------
"""

    # Générer le tableau avec différentiels
    for (i, item) in enumerate(evolution)
        simple_theo = get(item, "simple_entropy_theoretical", 0)
        index5_value = i <= length(sequence) ? sequence[i] : "N/A"
        prediction_display = i <= length(predictions) ? predictions[i] : "N/A"

        # Récupérer les différentiels correspondants
        if i <= length(differentials)
            diff_data = differentials[i]
            diff_cond = get(diff_data, "diff_conditional", 0)
            diff_taux = get(diff_data, "diff_entropy_rate", 0)
            diff_div_entrop = get(diff_data, "diff_simple_entropy", 0)
            diff_entrop = get(diff_data, "diff_simple_entropy_theoretical", 0)
        else
            diff_cond = diff_taux = diff_div_entrop = diff_entrop = 0.0
        end

        report *= @sprintf("Main %2d  | %11s | %6.3f  | %12.3f  | %4.3f | %9.3f | %10.3f | %6.3f   | %6.3f   | %12.3f   | %9.3f   | %14s\n",
            item["position"], index5_value, item["metric_entropy"], item["conditional_entropy"],
            item["entropy_rate"], item["simple_entropy"], simple_theo, diff_cond, diff_taux,
            diff_div_entrop, diff_entrop, prediction_display)
    end

    # Ajouter le nouveau tableau prédictif avec différentiels
    predictive_table_generator = INDEX5PredictiveDifferentialTable()
    predictive_table = generate_predictive_table(
        predictive_table_generator,
        analysis_result["full_sequence"],
        analysis_result["entropy_evolution"],
        analyzer
    )

    # CORRECTION : Injecter directement les données calculées dans le tableau SCORES
    computed_differentials = predictive_table_generator._differential_cache
    predictive_score_table_generator = INDEX5PredictiveScoreTable()
    predictive_score_table = generate_predictive_score_table(
        predictive_score_table_generator,
        analysis_result["full_sequence"],
        analysis_result["entropy_evolution"],
        analyzer,
        computed_differentials
    )

    report *= """

🔮 TABLEAU PRÉDICTIF - DIFFÉRENTIELS POUR LES 9 VALEURS INDEX5 POSSIBLES
═══════════════════════════════════════════════════════════════════════

$predictive_table

$predictive_score_table
"""

    # Ajouter les statistiques des différentiels
    diff_stats = get_differential_statistics(differential_analyzer, differentials)
    if !isempty(diff_stats)
        diff_cond_stats = get(diff_stats, "diff_conditional", Dict{String, Any}())
        diff_rate_stats = get(diff_stats, "diff_entropy_rate", Dict{String, Any}())
        diff_simple_stats = get(diff_stats, "diff_simple_entropy", Dict{String, Any}())
        diff_theo_stats = get(diff_stats, "diff_simple_entropy_theoretical", Dict{String, Any}())

        report *= """

📈 STATISTIQUES DES DIFFÉRENTIELS
═══════════════════════════════

🔢 DIFFÉRENTIELS ENTROPIE CONDITIONNELLE (DiffCond)
• Minimum: $(round(get(diff_cond_stats, "min", 0), digits=4)) bits
• Maximum: $(round(get(diff_cond_stats, "max", 0), digits=4)) bits
• Moyenne: $(round(get(diff_cond_stats, "mean", 0), digits=4)) bits
• Écart-type: $(round(get(diff_cond_stats, "std", 0), digits=4)) bits

🔢 DIFFÉRENTIELS TAUX D'ENTROPIE (DiffTaux)
• Minimum: $(round(get(diff_rate_stats, "min", 0), digits=4)) bits
• Maximum: $(round(get(diff_rate_stats, "max", 0), digits=4)) bits
• Moyenne: $(round(get(diff_rate_stats, "mean", 0), digits=4)) bits
• Écart-type: $(round(get(diff_rate_stats, "std", 0), digits=4)) bits

🔢 DIFFÉRENTIELS DIVERSITÉ ENTROPIQUE (DiffDivEntropG)
• Minimum: $(round(get(diff_simple_stats, "min", 0), digits=4)) bits
• Maximum: $(round(get(diff_simple_stats, "max", 0), digits=4)) bits
• Moyenne: $(round(get(diff_simple_stats, "mean", 0), digits=4)) bits
• Écart-type: $(round(get(diff_simple_stats, "std", 0), digits=4)) bits

🔢 DIFFÉRENTIELS ENTROPIE GÉNÉRALE (DiffEntropG)
• Minimum: $(round(get(diff_theo_stats, "min", 0), digits=4)) bits
• Maximum: $(round(get(diff_theo_stats, "max", 0), digits=4)) bits
• Moyenne: $(round(get(diff_theo_stats, "mean", 0), digits=4)) bits
• Écart-type: $(round(get(diff_theo_stats, "std", 0), digits=4)) bits
"""
    end

    # Ajout d'analyses statistiques détaillées
    min_metric_idx = argmin(metric_entropies)
    max_metric_idx = argmax(metric_entropies)
    min_cond_idx = argmin(conditional_entropies)
    max_cond_idx = argmax(conditional_entropies)

    report *= """
📊 ANALYSES STATISTIQUES COMPLÈTES
═══════════════════════════════════

🔢 STATISTIQUES D'ENTROPIE MÉTRIQUE
• Minimum: $(round(minimum(metric_entropies), digits=4)) bits (main $min_metric_idx)
• Maximum: $(round(maximum(metric_entropies), digits=4)) bits (main $max_metric_idx)
• Écart-type: $(round(std(metric_entropies), digits=4)) bits
• Coefficient de variation: $(round(std(metric_entropies)/mean(metric_entropies)*100, digits=1))%

🔢 STATISTIQUES D'ENTROPIE CONDITIONNELLE
• Minimum: $(round(minimum(conditional_entropies), digits=4)) bits (main $min_cond_idx)
• Maximum: $(round(maximum(conditional_entropies), digits=4)) bits (main $max_cond_idx)
• Écart-type: $(round(std(conditional_entropies), digits=4)) bits
• Coefficient de variation: $(round(std(conditional_entropies)/mean(conditional_entropies)*100, digits=1))%

🔢 ÉVOLUTION DE LA DIVERSITÉ
• Diversité initiale: $(evolution[1]["unique_values"])/18 ($(round(evolution[1]["unique_values"]/18*100, digits=1))%)
• Diversité finale: $(evolution[end]["unique_values"])/18 ($(round(evolution[end]["unique_values"]/18*100, digits=1))%)
• Croissance de diversité: +$(evolution[end]["unique_values"] - evolution[1]["unique_values"]) valeurs uniques

🎯 POINTS D'INTÉRÊT IDENTIFIÉS
• Main avec entropie métrique maximale: $max_metric_pos ($(round(maximum(metric_entropies), digits=4)) bits)
• Main avec entropie conditionnelle maximale: $max_conditional_pos ($(round(maximum(conditional_entropies), digits=4)) bits)
• Stabilisation de l'entropie métrique: $(length(metric_entropies) >= 10 && std(metric_entropies[end-9:end]) < 0.05 ? "Oui" : "Non") (10 dernières mains)

🔍 INTERPRÉTATION AVANCÉE
• Entropie métrique moyenne ($(round(mean(metric_entropies), digits=3)) bits) = $(round(mean(metric_entropies)/analyzer.theoretical_entropy*100, digits=1))% du maximum théorique
• Entropie conditionnelle faible → Forte dépendance temporelle, patterns récurrents exploitables
• Taux d'entropie stable → Information moyenne générée par symbole à long terme
• Complexité LZ ($(get(complexity, "lz_complexity", "N/A"))) → Séquence $(get(complexity, "lz_complexity", 60) < 40 ? "hautement" : "modérément") compressible
• Coefficient de variation faible → Comportement $(std(metric_entropies)/mean(metric_entropies) < 0.2 ? "stable" : "variable")

$(get_detailed_report(validator))
"""

    return report
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                  CATÉGORIE 9 : CHARGEMENT DE DONNÉES                         ║
# ║                          Lignes 1192-1240                                    ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    load_baccarat_data(analyzer::BaccaratEntropyAnalyzer, filepath::String)::Vector{Dict{String, Any}}

📁 CHARGEMENT - Charge les données de baccarat depuis le fichier JSON

Gère différentes structures JSON possibles et valide les données chargées.

# Arguments
- `filepath::String`: Chemin vers le fichier JSON

# Returns
- `Vector{Dict{String, Any}}`: Liste des parties de baccarat
"""
function load_baccarat_data(analyzer::BaccaratEntropyAnalyzer, filepath::String)::Vector{Dict{String, Any}}
    try
        # Lire le fichier JSON
        data = JSON.parsefile(filepath)

        # Vérifier la structure du JSON
        if isa(data, Dict) && haskey(data, "parties_condensees")
            # Structure: {"parties_condensees": [...]}
            parties = data["parties_condensees"]
            println("✅ Données chargées: $(length(parties)) parties trouvées")
            return parties
        elseif isa(data, Vector)
            # Structure: [partie1, partie2, ...]
            println("✅ Données chargées: $(length(data)) parties trouvées")
            return data
        else
            if isa(data, Dict)
                println("❌ Structure JSON non reconnue. Clés disponibles: $(collect(keys(data)))")
            else
                println("❌ Structure JSON non reconnue. Liste non détectée")
            end
            return Dict{String, Any}[]
        end

    catch e
        if isa(e, SystemError)
            println("❌ Erreur: Fichier $filepath non trouvé")
        elseif isa(e, ArgumentError) || isa(e, JSON.ParserError)
            println("❌ Erreur JSON: $e")
        else
            println("❌ Erreur inattendue: $e")
        end
        return Dict{String, Any}[]
    end
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                     CATÉGORIE 10 : VISUALISATION                             ║
# ║                          Lignes 1241-1304                                    ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    plot_entropy_evolution(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any}, save_path::Union{String, Nothing}=nothing)

Visualise l'évolution de l'entropie au cours d'une partie.

# Arguments
- `analysis_result::Dict{String, Any}`: Résultats d'analyse d'une partie
- `save_path::Union{String, Nothing}`: Chemin pour sauvegarder le graphique (optionnel)
"""
function plot_entropy_evolution(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any}, save_path::Union{String, Nothing}=nothing)
    if !haskey(analysis_result, "entropy_evolution")
        println("❌ Pas de données d'évolution d'entropie")
        return
    end

    evolution = analysis_result["entropy_evolution"]
    positions = [item["position"] for item in evolution]
    # CORRECTION: Utiliser 'simple_entropy' au lieu de 'empirical_entropy' (fréquences observées = entropie empirique)
    empirical_entropies = [item["simple_entropy"] for item in evolution]
    unique_values = [item["unique_values"] for item in evolution]

    # Configuration du graphique (nécessite Plots.jl)
    using Plots

    # Graphique 1: Évolution de l'entropie
    p1 = plot(positions, empirical_entropies,
              linewidth=2, marker=:circle, markersize=4,
              label="Entropie empirique", color=:blue,
              xlabel="Position dans la partie (main n)",
              ylabel="Entropie (bits)",
              title="Évolution de l'Entropie INDEX5 - Partie $(analysis_result["game_id"])",
              grid=true, gridwidth=1, gridcolor=:gray, gridalpha=0.3)

    hline!(p1, [analyzer.theoretical_entropy],
           linestyle=:dash, linewidth=2, color=:red,
           label="Entropie théorique max ($(round(analyzer.theoretical_entropy, digits=3)) bits)")

    # Graphique 2: Nombre de valeurs uniques
    p2 = plot(positions, unique_values,
              linewidth=2, marker=:square, markersize=4,
              label="Valeurs uniques observées", color=:green,
              xlabel="Position dans la partie (main n)",
              ylabel="Nombre de valeurs uniques",
              title="Évolution de la Diversité des Valeurs INDEX5",
              grid=true, gridwidth=1, gridcolor=:gray, gridalpha=0.3)

    hline!(p2, [18],
           linestyle=:dash, linewidth=2, color=:red,
           label="Maximum théorique (18 valeurs)")

    # Combiner les graphiques
    combined_plot = plot(p1, p2, layout=(2, 1), size=(800, 600))

    if save_path !== nothing
        savefig(combined_plot, save_path)
        println("📊 Graphique sauvegardé: $save_path")
    end

    display(combined_plot)
end