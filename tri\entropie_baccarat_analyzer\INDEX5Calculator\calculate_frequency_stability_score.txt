# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1631 à 1667
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_frequency_stability_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule un score de stabilité des fréquences INDEX5.
Compare les fréquences récentes avec les fréquences globales.
Plus le score est élevé, plus les fréquences sont stables.
"""
function calculate_frequency_stability_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 20
        return 0.0
    end

    # Fréquences globales
    global_freq = Dict{String, Int}()
    for value in sequence_history
        global_freq[value] = get(global_freq, value, 0) + 1
    end
    global_total = length(sequence_history)

    # Fréquences récentes (30 dernières observations)
    recent_sequence = length(sequence_history) >= 30 ? sequence_history[end-29:end] : sequence_history[end-div(length(sequence_history), 2)+1:end]
    recent_freq = Dict{String, Int}()
    for value in recent_sequence
        recent_freq[value] = get(recent_freq, value, 0) + 1
    end
    recent_total = length(recent_sequence)

    # Calculer la stabilité pour chaque valeur INDEX5
    stability_scores = Float64[]

    for index5_value in keys(calculator.THEORETICAL_PROBS)
        global_prob = get(global_freq, index5_value, 0) / global_total
        recent_prob = get(recent_freq, index5_value, 0) / recent_total

        # Score de stabilité = 1 - différence relative
        if global_prob > 0
            relative_diff = abs(recent_prob - global_prob) / global_prob
            stability_score = max(0.0, 1.0 - relative_diff)
            push!(stability_scores, stability_score)
        end
    end

    if !isempty(stability_scores)
        return round(sum(stability_scores) / length(stability_scores), digits=4)
    end

    return 0.0
end