# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 724 à 769
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    plot_entropy_evolution(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any}, save_path::Union{String, Nothing}=nothing)

Visualise l'évolution de l'entropie au cours d'une partie.

# Arguments
- `analysis_result::Dict{String, Any}`: Résultats d'analyse d'une partie
- `save_path::Union{String, Nothing}`: Chemin pour sauvegarder le graphique (optionnel)
"""
function plot_entropy_evolution(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any}, save_path::Union{String, Nothing}=nothing)
    if !haskey(analysis_result, "entropy_evolution")
        println("❌ Pas de données d'évolution d'entropie")
        return
    end

    evolution = analysis_result["entropy_evolution"]
    positions = [item["position"] for item in evolution]
    # CORRECTION: Utiliser 'simple_entropy' au lieu de 'empirical_entropy' (fréquences observées = entropie empirique)
    empirical_entropies = [item["simple_entropy"] for item in evolution]
    unique_values = [item["unique_values"] for item in evolution]

    # Configuration du graphique (nécessite Plots.jl)
    using Plots

    # Graphique 1: Évolution de l'entropie
    p1 = plot(positions, empirical_entropies,
              linewidth=2, marker=:circle, markersize=4,
              label="Entropie empirique", color=:blue,
              xlabel="Position dans la partie (main n)",
              ylabel="Entropie (bits)",
              title="Évolution de l'Entropie INDEX5 - Partie $(analysis_result["game_id"])",
              grid=true, gridwidth=1, gridcolor=:gray, gridalpha=0.3)

    hline!(p1, [analyzer.theoretical_entropy],
           linestyle=:dash, linewidth=2, color=:red,
           label="Entropie théorique max ($(round(analyzer.theoretical_entropy, digits=3)) bits)")

    # Graphique 2: Nombre de valeurs uniques
    p2 = plot(positions, unique_values,
              linewidth=2, marker=:square, markersize=4,
              label="Valeurs uniques observées", color=:green,
              xlabel="Position dans la partie (main n)",
              ylabel="Nombre de valeurs uniques",
              title="Évolution de la Diversité des Valeurs INDEX5",
              grid=true, gridwidth=1, gridcolor=:gray, gridalpha=0.3)

    hline!(p2, [18],
           linestyle=:dash, linewidth=2, color=:red,
           label="Maximum théorique (18 valeurs)")

    # Combiner les graphiques
    combined_plot = plot(p1, p2, layout=(2, 1), size=(800, 600))

    if save_path !== nothing
        savefig(combined_plot, save_path)
        println("📊 Graphique sauvegardé: $save_path")
    end

    display(combined_plot)
end