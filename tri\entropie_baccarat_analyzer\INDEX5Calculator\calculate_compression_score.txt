# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1301 à 1326
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_compression_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64

Calcule un score de compressibilité basé sur la complexité LZ.
Plus le score est élevé, plus la séquence est compressible (patterns répétitifs).
"""
function calculate_compression_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64
    if length(sequence_history) < 4
        return 0.0
    end

    # Utiliser la complexité LZ si disponible
    lz_complexity = get(current_metrics, "lz_complexity", nothing)

    if lz_complexity !== nothing
        # Normaliser la complexité LZ (plus faible = plus compressible)
        max_complexity = length(sequence_history)  # Complexité maximale théorique
        compression_score = 1.0 - (lz_complexity / max_complexity)
        return round(max(0.0, compression_score), digits=4)
    end

    # Si pas de complexité LZ, utiliser une approximation basée sur les répétitions
    unique_elements = length(Set(sequence_history))
    total_elements = length(sequence_history)

    # Score basé sur la diversité (moins de diversité = plus compressible)
    diversity_ratio = unique_elements / total_elements
    compression_score = 1.0 - diversity_ratio

    return round(compression_score, digits=4)
end