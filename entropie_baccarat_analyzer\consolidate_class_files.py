#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour consolider tous les fichiers .txt de chaque classe en un seul fichier
Parcourt chaque dossier de classe et concatène tout le contenu en un fichier unique
"""

import os
import glob
from pathlib import Path

def consolidate_class_files(base_directory, clean_after=False):
    """
    Consolide tous les fichiers .txt de chaque dossier de classe en un seul fichier

    Args:
        base_directory: Dossier de base contenant les dossiers de classes
        clean_after: Si True, supprime les fichiers sources et dossiers vides après consolidation
    """
    base_path = Path(base_directory)

    if not base_path.exists():
        print(f"❌ Le dossier de base n'existe pas: {base_directory}")
        return

    print(f"🔍 Analyse du dossier: {base_directory}")
    print("=" * 80)

    # Obtenir tous les sous-dossiers (classes)
    class_directories = [d for d in base_path.iterdir() if d.is_dir()]

    if not class_directories:
        print("❌ Aucun dossier de classe trouvé")
        return

    print(f"📁 {len(class_directories)} dossiers de classe trouvés:")
    for class_dir in sorted(class_directories):
        print(f"   - {class_dir.name}")

    if clean_after:
        print("🧹 Mode nettoyage activé - les fichiers sources seront supprimés après consolidation")

    print("\n" + "=" * 80)
    
    # Traiter chaque classe
    total_files_processed = 0
    total_classes_processed = 0
    
    for class_dir in sorted(class_directories):
        class_name = class_dir.name
        print(f"\n🔄 Traitement de la classe: {class_name}")
        
        # Trouver tous les fichiers .txt dans ce dossier
        txt_files = list(class_dir.glob("*.txt"))
        
        if not txt_files:
            print(f"   ⚠️  Aucun fichier .txt trouvé dans {class_name}")
            continue
        
        print(f"   📄 {len(txt_files)} fichiers .txt trouvés")
        
        # Créer le fichier consolidé
        consolidated_filename = f"{class_name}_consolidated.txt"
        consolidated_path = base_path / consolidated_filename
        
        try:
            with open(consolidated_path, 'w', encoding='utf-8') as consolidated_file:
                # En-tête du fichier consolidé
                consolidated_file.write(f"# =" * 40 + "\n")
                consolidated_file.write(f"# FICHIER CONSOLIDÉ POUR LA CLASSE: {class_name}\n")
                consolidated_file.write(f"# Généré automatiquement - {len(txt_files)} fichiers sources\n")
                consolidated_file.write(f"# =" * 40 + "\n\n")
                
                # Traiter chaque fichier .txt
                for i, txt_file in enumerate(sorted(txt_files), 1):
                    print(f"   📝 [{i:2d}/{len(txt_files)}] {txt_file.name}")
                    
                    # Ajouter un séparateur pour chaque fichier
                    consolidated_file.write(f"# " + "-" * 60 + "\n")
                    consolidated_file.write(f"# FICHIER SOURCE: {txt_file.name}\n")
                    consolidated_file.write(f"# " + "-" * 60 + "\n\n")
                    
                    try:
                        # Lire et écrire le contenu du fichier
                        with open(txt_file, 'r', encoding='utf-8') as source_file:
                            content = source_file.read()
                            
                            # Vérifier si le fichier est vide ou ne contient qu'une ligne
                            if content.strip():
                                consolidated_file.write(content)
                                if not content.endswith('\n'):
                                    consolidated_file.write('\n')
                            else:
                                consolidated_file.write("# [FICHIER VIDE OU SUPPRIMÉ]\n")
                        
                        # Ajouter un espacement entre les fichiers
                        consolidated_file.write("\n\n")
                        
                    except Exception as e:
                        print(f"   ❌ Erreur lors de la lecture de {txt_file.name}: {e}")
                        consolidated_file.write(f"# [ERREUR LORS DE LA LECTURE: {e}]\n\n")
                
                # Pied de page
                consolidated_file.write(f"# " + "=" * 60 + "\n")
                consolidated_file.write(f"# FIN DU FICHIER CONSOLIDÉ POUR {class_name}\n")
                consolidated_file.write(f"# TOTAL: {len(txt_files)} fichiers sources traités\n")
                consolidated_file.write(f"# " + "=" * 60 + "\n")
            
            print(f"   ✅ Fichier consolidé créé: {consolidated_filename}")
            total_files_processed += len(txt_files)
            total_classes_processed += 1

            # Nettoyage optionnel des fichiers sources
            if clean_after:
                print(f"   🧹 Suppression des fichiers sources...")
                files_deleted = 0
                for txt_file in txt_files:
                    try:
                        txt_file.unlink()  # Supprimer le fichier
                        files_deleted += 1
                    except Exception as del_e:
                        print(f"   ⚠️  Impossible de supprimer {txt_file.name}: {del_e}")

                print(f"   ✅ {files_deleted}/{len(txt_files)} fichiers sources supprimés")

                # Supprimer le dossier s'il est vide
                try:
                    if not any(class_dir.iterdir()):  # Dossier vide
                        class_dir.rmdir()
                        print(f"   🗂️  Dossier vide supprimé: {class_name}")
                except Exception as dir_e:
                    print(f"   ⚠️  Impossible de supprimer le dossier {class_name}: {dir_e}")

        except Exception as e:
            print(f"   ❌ Erreur lors de la création du fichier consolidé: {e}")
    
    # Résumé final
    print("\n" + "=" * 80)
    print("✅ CONSOLIDATION TERMINÉE!")
    print("=" * 80)
    print(f"📊 {total_classes_processed} classes traitées")
    print(f"📄 {total_files_processed} fichiers sources consolidés")
    print(f"📁 Fichiers consolidés créés dans: {base_directory}")
    
    # Lister les fichiers consolidés créés
    consolidated_files = list(base_path.glob("*_consolidated.txt"))
    if consolidated_files:
        print(f"\n📋 Fichiers consolidés créés:")
        for consolidated_file in sorted(consolidated_files):
            file_size = consolidated_file.stat().st_size
            print(f"   - {consolidated_file.name} ({file_size:,} octets)")

def main():
    """
    Point d'entrée principal
    """
    print("🎯 CONSOLIDATEUR DE FICHIERS PAR CLASSE")
    print("=" * 80)
    
    # Dossier de base contenant les dossiers de classes
    base_directory = r"C:\Users\<USER>\Desktop\11\entropie_baccarat_analyzer"
    
    # Vérifier que le dossier existe
    if not os.path.exists(base_directory):
        print(f"❌ Le dossier de base n'existe pas: {base_directory}")
        return
    
    # Demander confirmation
    print(f"📁 Dossier de base: {base_directory}")
    response = input("\n🤔 Voulez-vous procéder à la consolidation? (o/n): ").lower().strip()

    if response != 'o':
        print("❌ Opération annulée")
        return

    # Demander si on veut nettoyer après consolidation
    clean_response = input("\n🧹 Voulez-vous supprimer les fichiers sources et dossiers vides après consolidation? (o/n): ").lower().strip()
    clean_after = clean_response == 'o'

    if clean_after:
        print("⚠️  ATTENTION: Les fichiers sources seront DÉFINITIVEMENT supprimés!")
        confirm = input("Êtes-vous sûr? (oui/non): ").lower().strip()
        if confirm != 'oui':
            clean_after = False
            print("🔒 Mode nettoyage désactivé - les fichiers sources seront conservés")

    # Consolider les fichiers
    consolidate_class_files(base_directory, clean_after)

if __name__ == "__main__":
    main()
