# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2138 à 2163
# Type: Méthode de la classe INDEX5Predictor

"""
    predict_deterministic_patterns(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}

Exploite les patterns récurrents détectés
"""
function predict_deterministic_patterns(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}
    pattern_predictions = Dict{String, Float64}()

    for pattern_length in 2:5
        if length(sequence_history) >= pattern_length
            current_pattern = sequence_history[end-pattern_length+1:end]

            # Chercher ce pattern dans l'historique
            continuations = find_pattern_continuations(predictor, current_pattern, sequence_history)

            if !isempty(continuations)
                # Pondérer par fréquence et récence
                for (continuation, freq) in continuations
                    weight = freq * (1.0 / pattern_length)  # Patterns courts = plus fiables
                    pattern_predictions[continuation] = get(pattern_predictions, continuation, 0.0) + weight
                end
            end
        end
    end

    if !isempty(pattern_predictions)
        # Normaliser et retourner le meilleur
        total_weight = sum(values(pattern_predictions))
        normalized = Dict{String, Float64}()
        for (k, v) in pattern_predictions
            normalized[k] = v / total_weight
        end
        return argmax(normalized)
    end

    return nothing
end