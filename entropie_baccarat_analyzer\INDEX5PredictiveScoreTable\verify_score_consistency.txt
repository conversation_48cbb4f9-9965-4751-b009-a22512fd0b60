# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2598 à 2626
# Type: Méthode de la classe INDEX5PredictiveScoreTable

"""
    verify_score_consistency(table::INDEX5PredictiveScoreTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, position::Int, index5_value::String)::Union{Dict{String, Any}, Nothing}

Méthode de vérification pour s'assurer que les SCORES correspondent aux différentiels
"""
function verify_score_consistency(table::INDEX5PredictiveScoreTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, position::Int, index5_value::String)::Union{Dict{String, Any}, Nothing}
    differential_table = INDEX5PredictiveDifferentialTable()
    predictive_diffs = calculate_predictive_differentials(
        differential_table, sequence, evolution, position, analyzer
    )

    if haskey(predictive_diffs, index5_value)
        diffs = predictive_diffs[index5_value]
        calculated_score = calculate_predictive_score(
            table.score_calculator,
            diffs["DiffCond"],
            diffs["DiffTaux"],
            diffs["DiffDivEntropG"],
            diffs["DiffEntropG"]
        )

        # Calcul manuel pour vérification
        manual_score = (diffs["DiffCond"] + diffs["DiffEntropG"]) / (diffs["DiffTaux"] + diffs["DiffDivEntropG"])

        return Dict{String, Any}(
            "diffs" => diffs,
            "calculated_score" => calculated_score,
            "manual_score" => manual_score,
            "match" => abs(calculated_score - manual_score) < 0.001
        )
    end

    return nothing
end