# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1524 à 1538
# Type: Méthode de la classe INDEX5Calculator

"""
    find_pattern_continuations(calculator::INDEX5Calculator, pattern::Vector{String}, sequence_history::Vector{String})::Dict{String, Int}

Trouve toutes les continuations d'un pattern dans l'historique
"""
function find_pattern_continuations(calculator::INDEX5Calculator, pattern::Vector{String}, sequence_history::Vector{String})::Dict{String, Int}
    continuations = Dict{String, Int}()
    pattern_len = length(pattern)

    for i in 1:(length(sequence_history) - pattern_len)
        if sequence_history[i:i+pattern_len-1] == pattern
            # Si il y a une continuation après ce pattern
            if i + pattern_len <= length(sequence_history)
                next_value = sequence_history[i + pattern_len]
                continuations[next_value] = get(continuations, next_value, 0) + 1
            end
        end
    end

    return continuations
end