module INDEX5PredictiveScoreTable

using Printf
import ..INDEX5PredictiveScoreCalculator: INDEX5PredictiveScoreCalculator, calculate_predictive_score
import ..INDEX5PredictiveDifferentialTable: INDEX5PredictiveDifferentialTable, calculate_predictive_differentials

export INDEX5PredictiveScoreTable, generate_predictive_score_table,
       generate_predictive_score_table_part, verify_score_consistency

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                    CATÉGORIE 1 : STRUCTURE ET INITIALISATION                 ║
# ║                           Lignes 1-23                                        ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    INDEX5PredictiveScoreTable()

Initialisation de la classe tableau prédictif avec scores
"""
mutable struct INDEX5PredictiveScoreTable
    all_index5_values::Vector{String}
    score_calculator::INDEX5PredictiveScoreCalculator

    function INDEX5PredictiveScoreTable()
        all_index5_values = [
            "0_A_BANKER", "0_B_BANKER", "0_C_BANKER",
            "0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER",
            "0_A_TIE", "0_B_TIE", "0_C_TIE",
            "1_A_BANKER", "1_B_BANKER", "1_C_BANKER",
            "1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER",
            "1_A_TIE", "1_B_TIE", "1_C_TIE"
        ]
        score_calculator = INDEX5PredictiveScoreCalculator()

        new(all_index5_values, score_calculator)
    end
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                 CATÉGORIE 2 : GÉNÉRATION DES TABLEAUX DE SCORES              ║
# ║                           Lignes 24-63                                       ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    generate_predictive_score_table(table::INDEX5PredictiveScoreTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, precomputed_differentials::Union{Dict, Nothing}=nothing)::String

Génère le tableau prédictif complet avec SCORES divisé en deux parties
CORRECTION : Utilise les différentiels pré-calculés pour garantir la synchronisation parfaite
"""
function generate_predictive_score_table(table::INDEX5PredictiveScoreTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, precomputed_differentials::Union{Dict, Nothing}=nothing)::String
    if isempty(sequence) || isempty(evolution)
        return "❌ Données insuffisantes pour générer le tableau prédictif"
    end

    # Générer la première partie (Mains 1-30)
    table_part1 = generate_predictive_score_table_part(table, sequence, evolution, analyzer, 1, 30, 1, precomputed_differentials)

    # Générer la deuxième partie (Mains 31-60)
    table_part2 = generate_predictive_score_table_part(table, sequence, evolution, analyzer, 31, 60, 2, precomputed_differentials)

    # Combiner les deux parties avec la légende
    complete_table = table_part1 * "\n\n" * table_part2 * """

📋 LÉGENDE DU TABLEAU PRÉDICTIF AVEC SCORES :
• SCORE = (DiffC + EntG) / (DiffT + DivEG)
• DiffC = DiffCond (Différentiel Entropie Conditionnelle)
• DiffT = DiffTaux (Différentiel Taux d'Entropie)
• DivEG = DiffDivEntropG (Différentiel Diversité Entropique)
• EntG = DiffEntropG (Différentiel Entropie Générale)
• Seules les 9 valeurs INDEX5 respectant les règles INDEX1 sont calculées
• N/A = Valeur non calculable (ne respecte pas les règles INDEX1)
• INF = Score infini (dénominateur = 0)
• --- = Main non disponible dans cette partie

🔄 RÈGLES INDEX1 DÉTERMINISTES :
• Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
• Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""

    return complete_table
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║               CATÉGORIE 3 : GÉNÉRATION DES PARTIES DE TABLEAUX               ║
# ║                          Lignes 64-192                                       ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    generate_predictive_score_table_part(table::INDEX5PredictiveScoreTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, start_main::Int, end_main::Int, part_number::Int, precomputed_differentials::Union{Dict, Nothing}=nothing)::String

Génère une partie du tableau prédictif avec SCORES au lieu des différentiels
CORRECTION : Utilise les différentiels pré-calculés pour synchronisation parfaite
"""
function generate_predictive_score_table_part(table::INDEX5PredictiveScoreTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, start_main::Int, end_main::Int, part_number::Int, precomputed_differentials::Union{Dict, Nothing}=nothing)::String
    if isempty(sequence) || isempty(evolution)
        return "❌ Données insuffisantes pour générer le tableau prédictif"
    end

    # Pour le premier tableau, commencer à la main 6 au lieu de la main 1
    if part_number == 1
        actual_start = max(start_main, 6)  # Commencer à la main 6
    else
        actual_start = start_main
    end

    # Ajuster les limites selon la longueur de la séquence
    actual_end = min(end_main, length(sequence))
    if actual_start > length(sequence)
        return "❌ Main $actual_start dépasse la longueur de la séquence ($(length(sequence)) mains)"
    end

    # Créer les en-têtes avec séparateurs verticaux
    header_line1 = "               |"
    header_line2 = "               |"
    header_separator = "===============|"
    separator_line = "===============|"

    for main_num in actual_start:actual_end
        header_line1 *= rpad("Main $main_num", 12) * "|"
        header_line2 *= rpad("SCORE", 12) * "|"
        header_separator *= "=" ^ 12 * "|"
        separator_line *= "=" ^ 12 * "|"
    end

    table_str = "🎯 TABLEAU PRÉDICTIF - SCORES POUR LES 9 VALEURS INDEX5 POSSIBLES\n"
    table_str *= "📊 PARTIE $part_number - MAINS $actual_start À $actual_end\n"
    table_str *= separator_line * "\n"
    table_str *= header_line1 * "\n"
    table_str *= header_separator * "\n"
    table_str *= header_line2 * "\n"
    table_str *= separator_line * "\n"

    # CORRECTION : Utiliser directement les différentiels pré-calculés
    if precomputed_differentials === nothing
        return "❌ Aucun différentiel pré-calculé fourni pour la synchronisation"
    end

    # CORRECTION : Extraire les données du cache avec la bonne structure
    # Le cache utilise des clés (tuple(sequence), position, id(analyzer))
    # Il faut les convertir en structure {position: data}
    all_predictive_diffs = Dict{Int, Any}()
    for (cache_key, cache_data) in precomputed_differentials
        if length(cache_key) == 3  # (tuple(sequence), position, id(analyzer))
            position = cache_key[2]
            all_predictive_diffs[position] = cache_data
        end
    end

    # Générer les lignes pour chaque valeur INDEX5 possible avec SCORES
    for (i, index5_value) in enumerate(table.all_index5_values)
        line = rpad(index5_value, 15)

        for position in (actual_start - 1):(actual_end - 1)  # -1 car les indices commencent à 1 en Julia
            line *= "|"  # Séparateur vertical avant chaque main

            if position + 1 <= length(sequence) && haskey(all_predictive_diffs, position)
                predictive_diffs = all_predictive_diffs[position]

                if haskey(predictive_diffs, index5_value)
                    diffs = predictive_diffs[index5_value]
                    # Calculer le SCORE au lieu d'afficher les différentiels
                    score = calculate_predictive_score(
                        table.score_calculator,
                        diffs["DiffCond"],
                        diffs["DiffTaux"],
                        diffs["DiffDivEntropG"],
                        diffs["DiffEntropG"]
                    )

                    # Formater le score avec 4 chiffres après la virgule
                    if score == Inf
                        cell_content = "INF"
                    else
                        cell_content = @sprintf("%.4f", score)
                    end
                else
                    cell_content = "N/A"
                end
            else
                cell_content = "---"
            end

            line *= rpad(cell_content, 12)
        end

        line *= "|"  # Séparateur final
        table_str *= line * "\n"

        # Ajouter un séparateur horizontal entre 0_C_TIE et 1_A_BANKER
        if index5_value == "0_C_TIE"
            table_str *= separator_line * "\n"
        end
    end

    # Ajouter la ligne OBSERVÉ
    table_str *= separator_line * "\n"
    observed_line = "OBSERVÉ        |"

    for position in (actual_start - 1):(actual_end - 1)
        if position + 1 <= length(sequence)
            observed_value = sequence[position + 1]
            cell_content = length(observed_value) > 10 ? observed_value[1:10] : observed_value  # Tronquer si trop long
        else
            cell_content = "---"
        end

        observed_line *= rpad(cell_content, 12) * "|"
    end

    table_str *= observed_line * "\n"
    table_str *= separator_line * "\n"

    return table_str
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                   CATÉGORIE 4 : VÉRIFICATION ET VALIDATION                   ║
# ║                          Lignes 193-228                                      ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    verify_score_consistency(table::INDEX5PredictiveScoreTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, position::Int, index5_value::String)::Union{Dict{String, Any}, Nothing}

Méthode de vérification pour s'assurer que les SCORES correspondent aux différentiels
"""
function verify_score_consistency(table::INDEX5PredictiveScoreTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, position::Int, index5_value::String)::Union{Dict{String, Any}, Nothing}
    differential_table = INDEX5PredictiveDifferentialTable()
    predictive_diffs = calculate_predictive_differentials(
        differential_table, sequence, evolution, position, analyzer
    )

    if haskey(predictive_diffs, index5_value)
        diffs = predictive_diffs[index5_value]
        calculated_score = calculate_predictive_score(
            table.score_calculator,
            diffs["DiffCond"],
            diffs["DiffTaux"],
            diffs["DiffDivEntropG"],
            diffs["DiffEntropG"]
        )

        # Calcul manuel pour vérification
        manual_score = (diffs["DiffCond"] + diffs["DiffEntropG"]) / (diffs["DiffTaux"] + diffs["DiffDivEntropG"])

        return Dict{String, Any}(
            "diffs" => diffs,
            "calculated_score" => calculated_score,
            "manual_score" => manual_score,
            "match" => abs(calculated_score - manual_score) < 0.001
        )
    end

    return nothing
end

end # module INDEX5PredictiveScoreTable