# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2040 à 2047
# Type: Méthode de la classe INDEX5Predictor

"""
    filter_prediction_by_constraint(predictor::INDEX5Predictor, prediction::Union{String, Nothing}, valid_values::Vector{String})::Union{String, Nothing}

Filtre une prédiction selon les contraintes INDEX1
Retourne la prédiction si valide, Nothing sinon
"""
function filter_prediction_by_constraint(predictor::INDEX5Predictor, prediction::Union{String, Nothing}, valid_values::Vector{String})::Union{String, Nothing}
    if prediction !== nothing && prediction in valid_values
        return prediction
    end
    return nothing
end