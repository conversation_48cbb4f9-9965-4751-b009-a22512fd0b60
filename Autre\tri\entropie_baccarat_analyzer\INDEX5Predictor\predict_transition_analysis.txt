# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2215 à 2247
# Type: Méthode de la classe INDEX5Predictor

"""
    predict_transition_analysis(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}

Analyse les transitions conditionnelles INDEX5
"""
function predict_transition_analysis(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}
    # Construire matrice de transitions
    transitions = Dict{String, Dict{String, Int}}()

    for i in 1:(length(sequence_history) - 1)
        current = sequence_history[i]
        next_val = sequence_history[i + 1]

        if !haskey(transitions, current)
            transitions[current] = Dict{String, Int}()
        end
        transitions[current][next_val] = get(transitions[current], next_val, 0) + 1
    end

    # Prédire basé sur la dernière valeur
    if !isempty(sequence_history)
        last_value = sequence_history[end]

        if haskey(transitions, last_value)
            # Normaliser les transitions depuis cette valeur
            total_transitions = sum(values(transitions[last_value]))
            transition_probs = Dict{String, Float64}()

            for (next_val, count) in transitions[last_value]
                transition_probs[next_val] = count / total_transitions
            end

            if !isempty(transition_probs)
                return argmax(transition_probs)
            end
        end
    end

    return nothing
end