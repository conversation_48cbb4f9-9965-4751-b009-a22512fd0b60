# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1906 à 1932
# Type: Méthode de la classe INDEX5Predictor

"""
    predict_bayesian_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, observed_frequencies::Dict{String, Any})::Union{String, Nothing}

Prédiction bayésienne utilisant probabilités théoriques INDEX5
"""
function predict_bayesian_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, observed_frequencies::Dict{String, Any})::Union{String, Nothing}
    # 1. Calculer probabilités conditionnelles observées
    conditional_probs = calculate_conditional_probabilities(predictor, sequence_history)

    # 2. Pondérer avec probabilités théoriques (Bayes)
    bayesian_probs = Dict{String, Float64}()
    for index5_value in keys(predictor.THEORETICAL_PROBS)
        # P(Xₙ₊₁|contexte) ∝ P(contexte|Xₙ₊₁) × P(Xₙ₊₁)
        observed_prob = get(conditional_probs, index5_value, 0.0)
        theoretical_prob = predictor.THEORETICAL_PROBS[index5_value]

        bayesian_prob = observed_prob * theoretical_prob
        bayesian_probs[index5_value] = bayesian_prob
    end

    # Normaliser les probabilités
    total_prob = sum(values(bayesian_probs))
    if total_prob > 0
        normalized_probs = Dict{String, Float64}()
        for (k, v) in bayesian_probs
            normalized_probs[k] = v / total_prob
        end

        # Retourner la valeur avec la plus haute probabilité
        best_prediction = argmax(normalized_probs)
        return best_prediction
    end

    return nothing
end