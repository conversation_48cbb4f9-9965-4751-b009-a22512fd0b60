# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2215 à 2247
# Type: Méthode de la classe INDEX5Predictor

    def predict_transition_analysis(self, sequence_history, metrics):
        """
        Analyse les transitions conditionnelles INDEX5
        """
        from collections import Counter

        # Construire matrice de transitions
        transitions = {}

        for i in range(len(sequence_history) - 1):
            current = sequence_history[i]
            next_val = sequence_history[i + 1]

            if current not in transitions:
                transitions[current] = Counter()
            transitions[current][next_val] += 1

        # Prédire basé sur la dernière valeur
        if sequence_history:
            last_value = sequence_history[-1]

            if last_value in transitions:
                # Normaliser les transitions depuis cette valeur
                total_transitions = sum(transitions[last_value].values())
                transition_probs = {
                    next_val: count / total_transitions
                    for next_val, count in transitions[last_value].items()
                }

                if transition_probs:
                    return max(transition_probs.items(), key=lambda x: x[1])[0]

        return None