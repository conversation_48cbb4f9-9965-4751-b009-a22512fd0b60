# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1237 à 1251
# Type: Méthode de la classe INDEX5Calculator

    def count_pattern_occurrences(self, pattern, sequence_history):
        """
        Compte le nombre d'occurrences d'un pattern dans l'historique
        """
        if len(pattern) == 0:
            return 0

        pattern_len = len(pattern)
        count = 0

        for i in range(len(sequence_history) - pattern_len + 1):
            if sequence_history[i:i+pattern_len] == pattern:
                count += 1

        return count