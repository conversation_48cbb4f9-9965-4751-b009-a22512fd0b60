# 🟡 NIVEAU INTERMÉDIAIRE - Chapitre 2
## Théorème de Codage de Source et Compression Optimale

### 🎯 Objectifs de ce chapitre
- Maîtriser le théorème fondamental de codage de source
- Comprendre l'inégalité de Kraft et ses implications
- Étudier les algorithmes de codage optimaux (<PERSON><PERSON><PERSON>, Shannon-Fano)
- Analyser les limites théoriques de la compression

---

## 📐 Théorème Fondamental de Codage de Source

### Énoncé du Théorème (Shannon, 1948)

**Pour une source discrète sans mémoire avec entropie H(X) :**

```
H(X) ≤ L̄ < H(X) + 1
```

**Où** :
- **L̄** : Longueur moyenne de codage optimale
- **H(X)** : Entropie de la source en bits

### Interprétation Fondamentale

> **L'entropie H(X) représente la limite théorique absolue de compression pour une source donnée.**

**Conséquences** :
1. **Borne inférieure** : Aucun code ne peut avoir une longueur moyenne < H(X)
2. **Borne supérieure** : Il existe toujours un code avec L̄ < H(X) + 1
3. **Optimalité** : Les codes optimaux approchent asymptotiquement H(X)

### Démonstration Esquissée

**Partie 1** : L̄ ≥ H(X) (Inégalité de Gibbs)
```
L̄ - H(X) = ∑ p(x) l(x) + ∑ p(x) log₂ p(x)
          = ∑ p(x) [l(x) - log₂(1/p(x))]
          = ∑ p(x) log₂(2^l(x) · p(x))
          ≥ log₂(∑ p(x) · 2^l(x) · p(x))  [Jensen]
          ≥ log₂(∑ p(x) · 2^(-l(x)) · 1)  [Kraft]
          ≥ 0
```

**Partie 2** : Construction d'un code avec L̄ < H(X) + 1
- Choisir l(x) = ⌈-log₂ p(x)⌉
- Vérifier l'inégalité de Kraft
- Calculer la longueur moyenne

---

## 🔧 Inégalité de Kraft

### Énoncé Mathématique

**Pour un code sans préfixe avec longueurs l₁, l₂, ..., lₙ :**

```
∑ᵢ₌₁ⁿ 2^(-lᵢ) ≤ 1
```

### Théorème de Kraft (Réciproque)

**Si ∑ᵢ₌₁ⁿ 2^(-lᵢ) ≤ 1, alors il existe un code sans préfixe avec ces longueurs.**

### Démonstration Constructive

**Algorithme de construction** :
1. Ordonner les longueurs : l₁ ≤ l₂ ≤ ... ≤ lₙ
2. Construire l'arbre binaire niveau par niveau
3. Assigner les mots de code par parcours en largeur

**Exemple** : Longueurs [1, 2, 2, 3]
```
Vérification Kraft : 2⁻¹ + 2⁻² + 2⁻² + 2⁻³ = 0.5 + 0.25 + 0.25 + 0.125 = 1.125 > 1
❌ Impossible !

Longueurs [2, 2, 2, 2] :
Vérification : 4 × 2⁻² = 1 ✓
Codes possibles : 00, 01, 10, 11
```

### Extension aux Codes Uniquement Décodables

**Théorème de McMillan** : L'inégalité de Kraft s'applique aussi aux codes uniquement décodables (pas seulement sans préfixe).

---

## 🌳 Algorithme de Huffman

### Principe de l'Algorithme

**Objectif** : Construire un code optimal (longueur moyenne minimale)

**Méthode** :
1. Créer une feuille pour chaque symbole avec sa probabilité
2. Répéter jusqu'à avoir un seul nœud :
   - Sélectionner les deux nœuds de probabilité minimale
   - Les fusionner en un nouveau nœud (somme des probabilités)
3. Assigner les codes par parcours de l'arbre (0 = gauche, 1 = droite)

### Exemple Détaillé

**Source** : A(0.4), B(0.3), C(0.2), D(0.1)

**Étapes de construction** :
```
Étape 1: [A:0.4] [B:0.3] [C:0.2] [D:0.1]
         Fusionner C et D → CD:0.3

Étape 2: [A:0.4] [B:0.3] [CD:0.3]
         Fusionner B et CD → BCD:0.6

Étape 3: [A:0.4] [BCD:0.6]
         Fusionner A et BCD → Root:1.0
```

**Arbre résultant** :
```
      Root(1.0)
     /         \
   A(0.4)    BCD(0.6)
              /      \
           B(0.3)   CD(0.3)
                    /     \
                 C(0.2)  D(0.1)
```

**Codes** : A=0, B=10, C=110, D=111

**Longueur moyenne** : L̄ = 0.4×1 + 0.3×2 + 0.2×3 + 0.1×3 = 1.9 bits

**Entropie** : H = -0.4log₂(0.4) - 0.3log₂(0.3) - 0.2log₂(0.2) - 0.1log₂(0.1) ≈ 1.85 bits

**Efficacité** : η = H/L̄ ≈ 97.4%

### Propriétés de l'Algorithme de Huffman

1. **Optimalité** : Produit toujours un code optimal
2. **Complexité** : O(n log n) avec une file de priorité
3. **Unicité** : Le code peut ne pas être unique (choix lors des égalités)
4. **Propriété de préfixe** : Toujours sans préfixe par construction

---

## 📊 Codage de Shannon-Fano

### Algorithme de Shannon

**Principe** : Assigner l(x) = ⌈-log₂ p(x)⌉

**Avantages** :
- Simple à calculer
- Garantit L̄ < H(X) + 1
- Pas besoin de construire un arbre

**Inconvénients** :
- Pas toujours optimal
- Peut gaspiller des bits

### Algorithme de Shannon-Fano

**Méthode récursive** :
1. Diviser les symboles en deux groupes de probabilités ≈ égales
2. Assigner 0 au premier groupe, 1 au second
3. Répéter récursivement sur chaque groupe

**Exemple** : A(0.4), B(0.3), C(0.2), D(0.1)
```
Groupe 1: A(0.4) → Code commence par 0
Groupe 2: B(0.3), C(0.2), D(0.1) → Code commence par 1

Subdivision du groupe 2:
  Sous-groupe 1: B(0.3) → Code 10
  Sous-groupe 2: C(0.2), D(0.1) → Code commence par 11
  
  Subdivision finale:
    C(0.2) → Code 110
    D(0.1) → Code 111
```

**Résultat** : A=0, B=10, C=110, D=111 (identique à Huffman dans ce cas)

---

## 🎯 Extensions et Variantes

### Codage de Blocs

**Principe** : Coder des séquences de n symboles au lieu de symboles individuels

**Avantage** : L̄ₙ/n → H(X) quand n → ∞

**Exemple** : Source binaire avec p(0) = 0.9, p(1) = 0.1
- Codage individuel : L̄ ≈ 0.47 bits/symbole
- Codage par paires : L̄₂/2 ≈ 0.44 bits/symbole
- Limite théorique : H(X) ≈ 0.47 bits/symbole

### Codage Arithmétique

**Principe** : Représenter tout le message par un seul nombre dans [0,1)

**Avantages** :
- Atteint exactement H(X) pour les messages longs
- Pas de contrainte sur les longueurs entières
- Optimal pour toute distribution

**Inconvénients** :
- Plus complexe à implémenter
- Sensible aux erreurs de transmission
- Nécessite une précision arithmétique élevée

### Codage de Lempel-Ziv

**Principe** : Exploiter les répétitions dans le texte (compression avec mémoire)

**Variantes** :
- LZ77 : Références vers le passé
- LZ78 : Construction d'un dictionnaire
- LZW : Utilisé dans GIF, TIFF

**Propriété** : Atteint asymptotiquement le taux d'entropie pour les sources ergodiques

---

## 📈 Analyse de Performance

### Métriques de Qualité

**Efficacité de compression** :
```
η = H(X) / L̄
```

**Redondance** :
```
R = L̄ - H(X)
```

**Taux de compression** :
```
τ = (Taille originale - Taille compressée) / Taille originale
```

### Comparaison des Algorithmes

| Algorithme | Optimalité | Complexité | Facilité |
|------------|------------|------------|----------|
| Huffman | Optimal | O(n log n) | Moyenne |
| Shannon | Sous-optimal | O(n) | Facile |
| Shannon-Fano | Sous-optimal | O(n log n) | Moyenne |
| Arithmétique | Optimal | O(n) | Difficile |

### Cas d'Usage Recommandés

**Huffman** : 
- Distributions connues et stables
- Besoin d'optimalité garantie
- Implémentation simple requise

**Arithmétique** :
- Messages longs
- Distributions variables
- Compression maximale nécessaire

**Lempel-Ziv** :
- Textes avec répétitions
- Distributions inconnues
- Compression universelle

---

## 🧮 Exercices Pratiques

### Exercice 1 : Vérification de Kraft

**Vérifiez si ces longueurs permettent un code sans préfixe :**
a) [1, 2, 3, 3, 3]
b) [2, 2, 2, 3, 3, 3]
c) [1, 1, 2, 2]

### Exercice 2 : Construction de Huffman

**Construisez l'arbre de Huffman pour :**
- Source : E(0.3), T(0.2), A(0.15), O(0.15), I(0.1), N(0.05), S(0.05)
- Calculez L̄ et comparez avec H(X)

### Exercice 3 : Optimisation

**Une source a 8 symboles équiprobables. Comparez :**
- Codage binaire fixe (3 bits/symbole)
- Codage de Huffman
- Limite théorique

### Exercice 4 : Codage Adaptatif

**Implémentez un algorithme de Huffman adaptatif qui met à jour l'arbre après chaque symbole transmis.**

---

## 🔍 Applications Avancées

### Compression d'Images

**JPEG** : Utilise le codage de Huffman après quantification DCT
**PNG** : Combine LZ77 et Huffman (DEFLATE)
**WebP** : Codage arithmétique moderne

### Compression Audio

**MP3** : Huffman sur les coefficients quantifiés
**FLAC** : LPC + codage de Rice (variante de Huffman)
**Opus** : Codage arithmétique adaptatif

### Compression Vidéo

**H.264/AVC** : CABAC (codage arithmétique binaire adaptatif)
**H.265/HEVC** : CABAC amélioré
**AV1** : Codage arithmétique avec modèles de contexte

### Archivage de Données

**ZIP** : DEFLATE (LZ77 + Huffman)
**7-Zip** : LZMA (LZ + codage arithmétique)
**Brotli** : LZ77 + Huffman avec dictionnaires prédéfinis

---

## 🔗 Vers le Chapitre Suivant

**Concepts maîtrisés** :
✅ Théorème de codage de source  
✅ Inégalité de Kraft  
✅ Algorithmes de codage optimaux  
✅ Analyse de performance  

**Prochaine étape** :
- Canaux de communication bruités
- Capacité de canal
- Théorème de codage de canal
- Codes correcteurs d'erreurs

---

## 📚 Points Clés à Retenir

✅ **H(X) ≤ L̄ < H(X) + 1** : Limites fondamentales de compression  
✅ **Kraft** : ∑ 2^(-lᵢ) ≤ 1 pour codes sans préfixe  
✅ **Huffman** : Algorithme optimal pour codage de symboles  
✅ **Efficacité** : η = H(X)/L̄ mesure la qualité du code  
✅ **Extensions** : Blocs, arithmétique, adaptatif pour améliorer les performances  

---

*Prochaine étape : [Chapitre 3 - Canaux de Communication](03_canaux_communication.md)*
