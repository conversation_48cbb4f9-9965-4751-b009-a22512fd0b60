# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1934 à 1963
# Type: Méthode de la classe INDEX5Predictor

"""
    calculate_conditional_probabilities(predictor::INDEX5Predictor, sequence_history::Vector{String})::Dict{String, Float64}

Calcule les probabilités conditionnelles observées
"""
function calculate_conditional_probabilities(predictor::INDEX5Predictor, sequence_history::Vector{String})::Dict{String, Float64}
    # Analyser les transitions depuis les 3 dernières valeurs
    context_transitions = Dict{Tuple, Dict{String, Int}}()

    for i in 4:length(sequence_history)
        context = Tuple(sequence_history[i-3:i-1])
        next_value = sequence_history[i]

        if context ∉ keys(context_transitions)
            context_transitions[context] = Dict{String, Int}()
        end

        if next_value ∉ keys(context_transitions[context])
            context_transitions[context][next_value] = 0
        end
        context_transitions[context][next_value] += 1
    end

    # Calculer probabilités conditionnelles pour le contexte actuel
    if length(sequence_history) >= 3
        current_context = Tuple(sequence_history[end-2:end])

        if haskey(context_transitions, current_context)
            transitions = context_transitions[current_context]
            total_transitions = sum(values(transitions))

            conditional_probs = Dict{String, Float64}()
            for (value, count) in transitions
                conditional_probs[value] = count / total_transitions
            end

            return conditional_probs
        end
    end

    return Dict{String, Float64}()
end