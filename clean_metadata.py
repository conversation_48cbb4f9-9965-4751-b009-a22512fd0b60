#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour supprimer les blocs de métadonnées des fichiers texte consolidés.

Supprime les blocs suivants :
# --------------------------------------------------
# FICHIER SOURCE: [nom].txt
# --------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: [chemin]
# Lignes: [numéros]
# Type: [type]
# Note: [note optionnelle]

"""

import os
import re
import glob
from pathlib import Path

def clean_metadata_from_file(file_path):
    """
    Supprime les blocs de métadonnées d'un fichier texte.
    
    Args:
        file_path (str): Chemin vers le fichier à nettoyer
    
    Returns:
        bool: True si le fichier a été modifié, False sinon
    """
    try:
        # Lire le fichier avec encodage UTF-8
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern pour détecter les blocs de métadonnées
        # Le pattern capture tout le bloc depuis "# --------------------------------------------------"
        # jusqu'à la ligne vide qui suit les métadonnées
        pattern = r'# --------------------------------------------------\n# FICHIER SOURCE: .*?\.txt\n# --------------------------------------------------\n\n# Emplacement exact dans le fichier source:\n# Fichier: .*?\n# Lignes: .*?\n# Type: .*?(?:\n# Note: .*?)?\n'

        # Supprimer tous les blocs de métadonnées
        cleaned_content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL)
        
        # Vérifier si le contenu a changé
        if cleaned_content != original_content:
            # Sauvegarder le fichier nettoyé
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_content)
            
            print(f"✅ Nettoyé: {file_path}")
            return True
        else:
            print(f"ℹ️  Aucun changement: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du traitement de {file_path}: {e}")
        return False

def clean_all_files_in_directory(directory_path):
    """
    Nettoie tous les fichiers .txt dans un répertoire et ses sous-répertoires.
    
    Args:
        directory_path (str): Chemin vers le répertoire à traiter
    """
    directory = Path(directory_path)
    
    if not directory.exists():
        print(f"❌ Le répertoire {directory_path} n'existe pas.")
        return
    
    # Trouver tous les fichiers .txt récursivement
    txt_files = list(directory.rglob("*.txt"))
    
    if not txt_files:
        print(f"ℹ️  Aucun fichier .txt trouvé dans {directory_path}")
        return
    
    print(f"🔍 Trouvé {len(txt_files)} fichier(s) .txt à traiter...")
    print()
    
    modified_count = 0
    
    for txt_file in txt_files:
        if clean_metadata_from_file(str(txt_file)):
            modified_count += 1
    
    print()
    print(f"📊 RÉSUMÉ:")
    print(f"   • Fichiers traités: {len(txt_files)}")
    print(f"   • Fichiers modifiés: {modified_count}")
    print(f"   • Fichiers inchangés: {len(txt_files) - modified_count}")

def main():
    """Fonction principale du script."""
    print("🧹 NETTOYEUR DE MÉTADONNÉES")
    print("=" * 50)
    
    # Répertoire cible
    target_directory = r"C:\Users\<USER>\Desktop\11\entropie_baccarat_analyzer_julia_groupe"
    
    print(f"📁 Répertoire cible: {target_directory}")
    print()
    
    # Nettoyer tous les fichiers
    clean_all_files_in_directory(target_directory)
    
    print()
    print("✨ Nettoyage terminé!")

if __name__ == "__main__":
    main()
