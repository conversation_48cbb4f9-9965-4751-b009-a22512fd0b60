# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3136 à 3168
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    get_accuracy_stats(validator::INDEX5PredictionValidator)::Dict{String, Any}

Retourne les statistiques de précision
"""
function get_accuracy_stats(validator::INDEX5PredictionValidator)::Dict{String, Any}
    if validator.total_predictions == 0
        return Dict{String, Any}(
            "correct_predictions" => 0,
            "total_predictions" => 0,
            "accuracy_percentage" => 0.0,
            "accuracy_ratio" => "0/0",
            "correct_predictions_high_confidence" => 0,
            "total_predictions_high_confidence" => 0,
            "accuracy_percentage_high_confidence" => 0.0,
            "accuracy_ratio_high_confidence" => "0/0"
        )
    end

    accuracy = (validator.correct_predictions / validator.total_predictions) * 100

    # Calculer la précision pour les prédictions haute confiance (>= 60% poids pondéré)
    accuracy_high_confidence = 0.0
    if validator.total_predictions_high_confidence > 0
        accuracy_high_confidence = (validator.correct_predictions_high_confidence / validator.total_predictions_high_confidence) * 100
    end

    return Dict{String, Any}(
        "correct_predictions" => validator.correct_predictions,
        "total_predictions" => validator.total_predictions,
        "accuracy_percentage" => accuracy,
        "accuracy_ratio" => "$(validator.correct_predictions)/$(validator.total_predictions)",
        "correct_predictions_high_confidence" => validator.correct_predictions_high_confidence,
        "total_predictions_high_confidence" => validator.total_predictions_high_confidence,
        "accuracy_percentage_high_confidence" => accuracy_high_confidence,
        "accuracy_ratio_high_confidence" => "$(validator.correct_predictions_high_confidence)/$(validator.total_predictions_high_confidence)"
    )
end