# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2347 à 2376
# Type: Méthode de la classe INDEX5DifferentialAnalyzer

"""
    get_differential_statistics(analyzer::INDEX5DifferentialAnalyzer, differentials::Vector{Dict{String, Any}})::Dict{String, Any}

Calcule les statistiques sur les différentiels.

# Arguments
- `differentials::Vector{Dict{String, Any}}`: Liste des différentiels calculés

# Returns
- `Dict{String, Any}`: Dictionnaire avec les statistiques
"""
function get_differential_statistics(analyzer::INDEX5DifferentialAnalyzer, differentials::Vector{Dict{String, Any}})::Dict{String, Any}
    if isempty(differentials) || length(differentials) < 2
        return Dict{String, Any}()
    end

    # Exclure la première main (différentiels = 0)
    valid_diffs = differentials[2:end]

    stats = Dict{String, Any}()

    for metric in ["diff_conditional", "diff_entropy_rate", "diff_simple_entropy", "diff_simple_entropy_theoretical"]
        values = [d[metric] for d in valid_diffs if haskey(d, metric)]

        if !isempty(values)
            mean_val = sum(values) / length(values)
            std_val = length(values) > 1 ? sqrt(sum((x - mean_val)^2 for x in values) / length(values)) : 0.0

            stats[metric] = Dict{String, Any}(
                "min" => minimum(values),
                "max" => maximum(values),
                "mean" => mean_val,
                "std" => std_val
            )
        end
    end

    return stats
end