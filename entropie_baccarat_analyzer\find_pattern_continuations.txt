# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1524 à 1538
# Type: Méthode de la classe INDEX5Calculator

    def find_pattern_continuations(self, pattern, sequence_history):
        """
        Trouve toutes les continuations d'un pattern dans l'historique
        """
        continuations = {}
        pattern_len = len(pattern)

        for i in range(len(sequence_history) - pattern_len):
            if sequence_history[i:i+pattern_len] == pattern:
                # Si il y a une continuation après ce pattern
                if i + pattern_len < len(sequence_history):
                    next_value = sequence_history[i + pattern_len]
                    continuations[next_value] = continuations.get(next_value, 0) + 1

        return continuations