# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                    CATÉGORIE 1 : STRUCTURE ET INITIALISATION                ║
# ║                           Lignes 1-31                                       ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    INDEX5Calculator(analyzer=nothing)

🔧 INITIALISATION - Configuration du calculateur INDEX5

Initialise les probabilités théoriques et la référence vers l'analyzer
pour accéder aux méthodes d'entropie AEP.
"""
mutable struct INDEX5Calculator
    analyzer::Union{BaccaratEntropyAnalyzer, Nothing}
    THEORETICAL_PROBS::Dict{String, Float64}

    function INDEX5Calculator(analyzer=nothing)
        # Référence vers l'analyzer pour accéder aux méthodes d'entropie

        # Probabilités théoriques INDEX5 (adaptées depuis BaccaratEntropyAnalyzer)
        THEORETICAL_PROBS = Dict{String, Float64}(
            "0_A_BANKER" => 0.085136, "1_A_BANKER" => 0.086389,
            "0_B_BANKER" => 0.064676, "1_B_BANKER" => 0.065479,
            "0_C_BANKER" => 0.077903, "1_C_BANKER" => 0.078929,
            "0_A_PLAYER" => 0.085240, "1_A_PLAYER" => 0.086361,
            "0_B_PLAYER" => 0.076907, "1_B_PLAYER" => 0.077888,
            "0_C_PLAYER" => 0.059617, "1_C_PLAYER" => 0.060352,
            "0_A_TIE" => 0.017719, "1_A_TIE" => 0.017978,
            "0_B_TIE" => 0.016281, "1_B_TIE" => 0.016482,
            "0_C_TIE" => 0.013241, "1_C_TIE" => 0.013423
        )

        new(analyzer, THEORETICAL_PROBS)
    end
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                  CATÉGORIE 2 : MÉTHODE PRINCIPALE D'ANALYSE                 ║
# ║                           Lignes 32-86                                      ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    calculate_all_metrics(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any}, entropy_evolution::Vector{Dict{String, Any}})::Dict{String, Any}

Calcule toutes les métriques disponibles pour une position donnée.
Retourne un dictionnaire avec tous les scores calculés.
"""
function calculate_all_metrics(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any}, entropy_evolution::Vector{Dict{String, Any}})::Dict{String, Any}
    if length(sequence_history) < 2
        return Dict{String, Any}()
    end

    metrics = Dict{String, Any}()

    # 1. Prédictibilité contextuelle
    metrics["context_predictability"] = calculate_context_predictability(calculator, sequence_history, current_metrics)

    # 2. Force des patterns
    metrics["pattern_strength"] = calculate_pattern_strength(calculator, sequence_history)

    # 3. Score de stabilité entropique
    if !isempty(entropy_evolution)
        metrics["entropy_stability"] = calculate_entropy_stability_score(calculator, entropy_evolution)
    end

    # 4. Score de compression
    metrics["compression_score"] = calculate_compression_score(calculator, sequence_history, current_metrics)

    # 5. Richesse structurelle
    metrics["structural_richness"] = calculate_structural_richness_score(calculator, sequence_history, current_metrics)

    # 6. Divergence bayésienne
    metrics["bayesian_divergence"] = calculate_bayesian_divergence_score(calculator, sequence_history)

    # 7. Entropie conditionnelle contextuelle
    metrics["conditional_entropy_context"] = calculate_conditional_entropy_context(calculator, sequence_history)

    # 8. Consensus multi-algorithmes
    metrics["multi_algorithm_consensus"] = calculate_multi_algorithm_consensus_score(calculator, sequence_history, current_metrics)

    # 9. Score de patterns déterministes
    metrics["deterministic_pattern_score"] = calculate_deterministic_pattern_score(calculator, sequence_history)

    # 10. Alignement bayésien théorique
    metrics["bayesian_theoretical_alignment"] = calculate_bayesian_theoretical_alignment(calculator, sequence_history)

    # 11. Entropie de la matrice de transitions
    metrics["transition_matrix_entropy"] = calculate_transition_matrix_entropy(calculator, sequence_history)

    # 12. Stabilité des fréquences
    metrics["frequency_stability"] = calculate_frequency_stability_score(calculator, sequence_history)

    return metrics
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                   CATÉGORIE 3 : MÉTRIQUES BAYÉSIENNES                      ║
# ║                          Lignes 87-181                                      ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    calculate_bayesian_divergence_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule la divergence entre les probabilités observées et théoriques INDEX5
en utilisant la divergence de Kullback-Leibler. Plus le score est élevé,
plus la séquence s'écarte des probabilités théoriques.
"""
function calculate_bayesian_divergence_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 10
        return 0.0
    end

    # 1. Calculer les fréquences observées
    observed_counts = Dict{String, Int}()
    for value in sequence_history
        observed_counts[value] = get(observed_counts, value, 0) + 1
    end
    total_observations = length(sequence_history)

    # 2. Calculer la divergence KL: D_KL(P_obs || P_theo) = Σ P_obs(x) log(P_obs(x) / P_theo(x))
    kl_divergence = 0.0

    for index5_value in keys(calculator.THEORETICAL_PROBS)
        p_theoretical = calculator.THEORETICAL_PROBS[index5_value]
        p_observed = get(observed_counts, index5_value, 0) / total_observations

        if p_observed > 0  # Éviter log(0)
            kl_divergence += p_observed * log2(p_observed / p_theoretical)
        end
    end

    # 3. Normaliser le score (la divergence KL peut être très élevée)
    # Utiliser une fonction sigmoïde pour normaliser entre 0 et 1
    normalized_score = 2 / (1 + exp(-kl_divergence)) - 1

    return round(max(0.0, normalized_score), digits=4)
end


"""
    calculate_bayesian_theoretical_alignment(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule l'alignement entre les observations récentes et les probabilités théoriques
en utilisant une approche bayésienne. Plus le score est élevé, plus les observations
récentes sont alignées avec la théorie.
"""
function calculate_bayesian_theoretical_alignment(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 10
        return 0.0
    end

    # Analyser les 20 dernières observations
    recent_sequence = length(sequence_history) >= 20 ? sequence_history[end-19:end] : sequence_history
    observed_freq = Dict{String, Int}()
    for value in recent_sequence
        observed_freq[value] = get(observed_freq, value, 0) + 1
    end

    # Calculer le score d'alignement bayésien
    alignment_scores = Float64[]

    for index5_value in keys(calculator.THEORETICAL_PROBS)
        p_theoretical = calculator.THEORETICAL_PROBS[index5_value]
        observed_count = get(observed_freq, index5_value, 0)
        p_observed = observed_count / length(recent_sequence)

        # Score d'alignement pour cette valeur (1 - différence relative)
        if p_theoretical > 0
            relative_diff = abs(p_observed - p_theoretical) / p_theoretical
            alignment_score = max(0.0, 1.0 - relative_diff)
            push!(alignment_scores, alignment_score)
        end
    end

    if !isempty(alignment_scores)
        # Score moyen pondéré par les probabilités théoriques
        weighted_alignment = 0.0
        total_weight = 0.0

        for (i, (index5_value, p_theoretical)) in enumerate(calculator.THEORETICAL_PROBS)
            if i <= length(alignment_scores)
                weighted_alignment += alignment_scores[i] * p_theoretical
                total_weight += p_theoretical
            end
        end

        if total_weight > 0
            return round(weighted_alignment / total_weight, digits=4)
        end
    end

    return 0.0
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                 CATÉGORIE 4 : MÉTRIQUES DE COMPRESSION ET CONTEXTE         ║
# ║                          Lignes 182-310                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    calculate_compression_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64

Calcule un score de compressibilité basé sur la complexité LZ.
Plus le score est élevé, plus la séquence est compressible (patterns répétitifs).
"""
function calculate_compression_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64
    if length(sequence_history) < 4
        return 0.0
    end

    # Utiliser la complexité LZ si disponible
    lz_complexity = get(current_metrics, "lz_complexity", nothing)

    if lz_complexity !== nothing
        # Normaliser la complexité LZ (plus faible = plus compressible)
        max_complexity = length(sequence_history)  # Complexité maximale théorique
        compression_score = 1.0 - (lz_complexity / max_complexity)
        return round(max(0.0, compression_score), digits=4)
    end

    # Si pas de complexité LZ, utiliser une approximation basée sur les répétitions
    unique_elements = length(Set(sequence_history))
    total_elements = length(sequence_history)

    # Score basé sur la diversité (moins de diversité = plus compressible)
    diversity_ratio = unique_elements / total_elements
    compression_score = 1.0 - diversity_ratio

    return round(compression_score, digits=4)
end


"""
    calculate_conditional_entropy_context(calculator::INDEX5Calculator, sequence_history::Vector{String}, context_length::Int=3)::Float64

Calcule l'entropie conditionnelle pour différents contextes.
Retourne l'entropie conditionnelle moyenne pour le contexte donné.
"""
function calculate_conditional_entropy_context(calculator::INDEX5Calculator, sequence_history::Vector{String}, context_length::Int=3)::Float64
    if length(sequence_history) <= context_length
        return 0.0
    end

    # Analyser les transitions depuis les contextes de longueur donnée
    context_transitions = Dict{Tuple, Dict{String, Int}}()

    for i in (context_length + 1):length(sequence_history)
        context = Tuple(sequence_history[i-context_length:i-1])
        next_value = sequence_history[i]

        if context ∉ keys(context_transitions)
            context_transitions[context] = Dict{String, Int}()
        end

        if next_value ∉ keys(context_transitions[context])
            context_transitions[context][next_value] = 0
        end
        context_transitions[context][next_value] += 1
    end

    # Calculer l'entropie conditionnelle H(X|Context)
    total_transitions = sum(sum(values(transitions)) for transitions in values(context_transitions))

    if total_transitions == 0
        return 0.0
    end

    conditional_entropy = 0.0

    for (context, transitions) in context_transitions
        context_total = sum(values(transitions))
        context_prob = context_total / total_transitions

        # CORRECTION AEP: Entropie pour ce contexte spécifique selon AEP
        # Créer la séquence des valeurs suivantes pour ce contexte
        context_sequence = String[]
        for (next_value, count) in transitions
            append!(context_sequence, repeat([next_value], count))
        end

        # Appeler la méthode depuis l'analyzer
        if calculator.analyzer !== nothing
            context_entropy = _calculate_sequence_entropy_aep(calculator.analyzer, context_sequence)
        else
            # Fallback si pas d'analyzer (ne devrait pas arriver)
            context_entropy = 0.0
        end

        # Pondérer par la probabilité du contexte
        conditional_entropy += context_prob * context_entropy
    end

    return round(conditional_entropy, digits=4)
end


"""
    calculate_context_predictability(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64

Calcule le niveau de prédictibilité contextuelle basé sur l'entropie conditionnelle
et les patterns récents. Retourne un score de prédictibilité contextuelle.
"""
function calculate_context_predictability(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64
    if length(sequence_history) < 5
        return 0.0
    end

    # 1. Score basé sur l'entropie conditionnelle (plus faible = plus prévisible)
    # CORRECTION AEP: Nouvelle normalisation basée sur les vraies plages observées
    conditional_entropy = get(current_metrics, "conditional_entropy", 6.2192)
    entropy_score = max(0.0, (6.2192 - conditional_entropy) / 6.2192)  # Normalisation corrigée

    # 2. Score basé sur la répétition des patterns récents
    recent_pattern = sequence_history[end-4:end]
    pattern_matches = count_pattern_occurrences(calculator, recent_pattern, sequence_history)
    pattern_score = min(pattern_matches / 10.0, 1.0)  # Normalisation sur 10 occurrences max

    # 3. Score basé sur le taux de répétition
    repetition_rate = get(current_metrics, "repetition_rate", 0)
    repetition_score = min(repetition_rate * 5, 1.0)  # Normalisation

    # 4. Score composite pondéré
    context_predictability = (0.5 * entropy_score + 0.3 * pattern_score + 0.2 * repetition_score)

    return round(context_predictability, digits=4)
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                CATÉGORIE 5 : MÉTRIQUES DE PATTERNS ET STABILITÉ            ║
# ║                          Lignes 311-432                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    calculate_deterministic_pattern_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule un score de déterminisme basé sur la récurrence des patterns.
Plus le score est élevé, plus la séquence présente des patterns déterministes.
"""
function calculate_deterministic_pattern_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 4
        return 0.0
    end

    pattern_scores = Float64[]

    # Analyser patterns de longueur 2 à 5
    for pattern_length in 2:min(5, length(sequence_history))
        current_pattern = sequence_history[end-pattern_length+1:end]

        # Chercher ce pattern dans l'historique (excluant la fin)
        continuations = find_pattern_continuations(calculator, current_pattern, sequence_history[1:end-pattern_length])

        if !isempty(continuations)
            # Calculer la prévisibilité de ce pattern
            total_continuations = sum(values(continuations))
            max_continuation = maximum(values(continuations))

            # Score = (fréquence max / total) * poids de longueur
            pattern_predictability = max_continuation / total_continuations
            length_weight = pattern_length / 5.0  # Normalisation

            pattern_score = pattern_predictability * length_weight
            push!(pattern_scores, pattern_score)
        end
    end

    if !isempty(pattern_scores)
        # Retourner le score maximum (meilleur pattern déterministe)
        return round(maximum(pattern_scores), digits=4)
    end

    return 0.0
end


"""
    calculate_entropy_stability_score(calculator::INDEX5Calculator, entropy_evolution::Vector{Dict{String, Any}})::Float64

Calcule un score de stabilité de l'entropie métrique.
Plus le score est élevé, plus l'entropie est stable (système déterministe).
"""
function calculate_entropy_stability_score(calculator::INDEX5Calculator, entropy_evolution::Vector{Dict{String, Any}})::Float64
    if length(entropy_evolution) < 5
        return 0.0
    end

    # Prendre les 10 dernières valeurs d'entropie métrique
    recent_evolution = length(entropy_evolution) >= 10 ? entropy_evolution[end-9:end] : entropy_evolution
    metric_entropies = [get(item, "metric_entropy", 0) for item in recent_evolution]

    if length(metric_entropies) < 2
        return 0.0
    end

    # Calculer la variance (plus faible = plus stable)
    variance = length(metric_entropies) > 0 ? var(metric_entropies) : 0.0

    # Convertir en score de stabilité (inverse de la variance, normalisé)
    stability_score = 1.0 / (1.0 + variance * 10)  # Normalisation

    return round(stability_score, digits=4)
end


"""
    calculate_frequency_stability_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule un score de stabilité des fréquences INDEX5.
Compare les fréquences récentes avec les fréquences globales.
Plus le score est élevé, plus les fréquences sont stables.
"""
function calculate_frequency_stability_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 20
        return 0.0
    end

    # Fréquences globales
    global_freq = Dict{String, Int}()
    for value in sequence_history
        global_freq[value] = get(global_freq, value, 0) + 1
    end
    global_total = length(sequence_history)

    # Fréquences récentes (30 dernières observations)
    recent_sequence = length(sequence_history) >= 30 ? sequence_history[end-29:end] : sequence_history[end-div(length(sequence_history), 2)+1:end]
    recent_freq = Dict{String, Int}()
    for value in recent_sequence
        recent_freq[value] = get(recent_freq, value, 0) + 1
    end
    recent_total = length(recent_sequence)

    # Calculer la stabilité pour chaque valeur INDEX5
    stability_scores = Float64[]

    for index5_value in keys(calculator.THEORETICAL_PROBS)
        global_prob = get(global_freq, index5_value, 0) / global_total
        recent_prob = get(recent_freq, index5_value, 0) / recent_total

        # Score de stabilité = 1 - différence relative
        if global_prob > 0
            relative_diff = abs(recent_prob - global_prob) / global_prob
            stability_score = max(0.0, 1.0 - relative_diff)
            push!(stability_scores, stability_score)
        end
    end

    if !isempty(stability_scores)
        return round(sum(stability_scores) / length(stability_scores), digits=4)
    end

    return 0.0
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║               CATÉGORIE 6 : MÉTRIQUES DE CONSENSUS ET RICHESSE              ║
# ║                          Lignes 433-562                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    calculate_multi_algorithm_consensus_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, all_metrics::Dict{String, Any})::Float64

Calcule un score de consensus entre différents algorithmes d'analyse.
Plus le score est élevé, plus les différentes méthodes sont en accord
sur les caractéristiques de la séquence.
"""
function calculate_multi_algorithm_consensus_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, all_metrics::Dict{String, Any})::Float64
    if isempty(sequence_history) || length(sequence_history) < 5
        return 0.0
    end

    # 1. Calculer différents scores avec les méthodes disponibles
    scores = Dict{String, Float64}()

    # Score de prédictibilité contextuelle
    context_score = calculate_context_predictability(calculator, sequence_history, all_metrics)
    scores["context"] = context_score

    # Score de force des patterns
    pattern_score = calculate_pattern_strength(calculator, sequence_history)
    scores["pattern"] = pattern_score

    # Score de compression
    compression_score = calculate_compression_score(calculator, sequence_history, all_metrics)
    scores["compression"] = compression_score

    # Score de divergence bayésienne (inverser pour cohérence)
    divergence_score = calculate_bayesian_divergence_score(calculator, sequence_history)
    scores["bayesian"] = 1.0 - divergence_score

    # 2. Calculer la variance des scores (faible variance = consensus élevé)
    score_values = collect(values(scores))
    if length(score_values) < 2
        return 0.0
    end

    mean_score = sum(score_values) / length(score_values)
    variance = sum((score - mean_score)^2 for score in score_values) / length(score_values)

    # 3. Convertir en score de consensus (faible variance = consensus élevé)
    consensus_score = 1.0 / (1.0 + variance * 10)  # Normalisation

    return round(consensus_score, digits=4)
end


"""
    calculate_pattern_strength(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule la force des patterns récurrents dans la séquence.
Retourne un score de force des patterns.
"""
function calculate_pattern_strength(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 4
        return 0.0
    end

    pattern_strengths = Float64[]

    # Analyser patterns de longueur 2 à 5
    for pattern_len in 2:min(5, length(sequence_history))
        recent_pattern = sequence_history[end-pattern_len+1:end]
        occurrences = count_pattern_occurrences(calculator, recent_pattern, sequence_history[1:end-pattern_len])

        if occurrences > 0
            # Force = occurrences pondérées par la longueur du pattern
            strength = occurrences * (pattern_len / 5.0)
            push!(pattern_strengths, strength)
        end
    end

    if !isempty(pattern_strengths)
        return round(maximum(pattern_strengths), digits=4)
    end

    return 0.0
end


"""
    calculate_structural_richness_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64

Calcule un score de richesse structurelle basé sur l'entropie topologique
et la diversité des patterns. Plus le score est élevé, plus la structure est riche.
"""
function calculate_structural_richness_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64
    if length(sequence_history) < 6
        return 0.0
    end

    # 1. Score basé sur l'entropie topologique si disponible
    topological_entropy = get(current_metrics, "topological_entropy", nothing)
    topo_score = 0.0

    if topological_entropy !== nothing
        # Normaliser l'entropie topologique (max théorique ≈ 4.17 pour INDEX5)
        topo_score = min(topological_entropy / 4.17, 1.0)
    end

    # 2. Score basé sur la diversité des patterns de différentes longueurs
    pattern_diversity_scores = Float64[]

    for pattern_len in 2:min(5, div(length(sequence_history), 2))
        patterns_found = Set{Tuple}()

        # Extraire tous les patterns de cette longueur
        for i in 1:(length(sequence_history) - pattern_len + 1)
            pattern = Tuple(sequence_history[i:i+pattern_len-1])
            push!(patterns_found, pattern)
        end

        # Score de diversité pour cette longueur
        max_possible_patterns = min(18^pattern_len, length(sequence_history) - pattern_len + 1)
        diversity_score = length(patterns_found) / max_possible_patterns
        push!(pattern_diversity_scores, diversity_score)
    end

    # 3. Score composite
    if !isempty(pattern_diversity_scores)
        avg_diversity = sum(pattern_diversity_scores) / length(pattern_diversity_scores)
        structural_richness = (0.6 * topo_score + 0.4 * avg_diversity)
    else
        structural_richness = topo_score
    end

    return round(structural_richness, digits=4)
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                CATÉGORIE 7 : MÉTRIQUES DE TRANSITIONS ET MATRICES          ║
# ║                          Lignes 563-625                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    calculate_transition_matrix_entropy(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule l'entropie de la matrice de transitions INDEX5.
Plus l'entropie est faible, plus les transitions sont prévisibles.
"""
function calculate_transition_matrix_entropy(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 3
        return 0.0
    end

    # Construire la matrice de transitions
    transitions = Dict{String, Dict{String, Int}}()

    for i in 1:(length(sequence_history) - 1)
        current = sequence_history[i]
        next_val = sequence_history[i + 1]

        if current ∉ keys(transitions)
            transitions[current] = Dict{String, Int}()
        end

        if next_val ∉ keys(transitions[current])
            transitions[current][next_val] = 0
        end
        transitions[current][next_val] += 1
    end

    # Calculer l'entropie de chaque ligne de la matrice
    total_entropy = 0.0
    total_weight = 0.0

    for (current_state, next_states) in transitions
        state_total = sum(values(next_states))
        state_weight = state_total / (length(sequence_history) - 1)

        # CORRECTION AEP: Entropie pour cet état selon AEP
        # Créer la séquence des états suivants pour cet état
        state_sequence = String[]
        for (next_state, count) in next_states
            append!(state_sequence, repeat([next_state], count))
        end

        # Appeler la méthode depuis l'analyzer
        if calculator.analyzer !== nothing
            state_entropy = _calculate_sequence_entropy_aep(calculator.analyzer, state_sequence)
        else
            # Fallback si pas d'analyzer (ne devrait pas arriver)
            state_entropy = 0.0
        end

        total_entropy += state_entropy * state_weight
        total_weight += state_weight
    end

    if total_weight > 0
        return round(total_entropy / total_weight, digits=4)
    end

    return 0.0
end

# ╔══════════════════════════════════════════════════════════════════════════════╗
# ║                    CATÉGORIE 8 : FONCTIONS UTILITAIRES                     ║
# ║                          Lignes 626-671                                     ║
# ╚══════════════════════════════════════════════════════════════════════════════╝

"""
    count_pattern_occurrences(calculator::INDEX5Calculator, pattern::Vector{String}, sequence_history::Vector{String})::Int

Compte le nombre d'occurrences d'un pattern dans l'historique
"""
function count_pattern_occurrences(calculator::INDEX5Calculator, pattern::Vector{String}, sequence_history::Vector{String})::Int
    if length(pattern) == 0
        return 0
    end

    pattern_len = length(pattern)
    count = 0

    for i in 1:(length(sequence_history) - pattern_len + 1)
        if sequence_history[i:i+pattern_len-1] == pattern
            count += 1
        end
    end

    return count
end


"""
    find_pattern_continuations(calculator::INDEX5Calculator, pattern::Vector{String}, sequence_history::Vector{String})::Dict{String, Int}

Trouve toutes les continuations d'un pattern dans l'historique
"""
function find_pattern_continuations(calculator::INDEX5Calculator, pattern::Vector{String}, sequence_history::Vector{String})::Dict{String, Int}
    continuations = Dict{String, Int}()
    pattern_len = length(pattern)

    for i in 1:(length(sequence_history) - pattern_len)
        if sequence_history[i:i+pattern_len-1] == pattern
            # Si il y a une continuation après ce pattern
            if i + pattern_len <= length(sequence_history)
                next_value = sequence_history[i + pattern_len]
                continuations[next_value] = get(continuations, next_value, 0) + 1
            end
        end
    end

    return continuations
end