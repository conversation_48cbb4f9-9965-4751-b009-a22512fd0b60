# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 193 à 228
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    load_baccarat_data(analyzer::BaccaratEntropyAnalyzer, filepath::String)::Vector{Dict{String, Any}}

📁 CHARGEMENT - Charge les données de baccarat depuis le fichier JSON

Gère différentes structures JSON possibles et valide les données chargées.

# Arguments
- `filepath::String`: Chemin vers le fichier JSON

# Returns
- `Vector{Dict{String, Any}}`: Liste des parties de baccarat
"""
function load_baccarat_data(analyzer::BaccaratEntropyAnalyzer, filepath::String)::Vector{Dict{String, Any}}
    try
        # Lire le fichier JSON
        data = JSON.parsefile(filepath)

        # Vérifier la structure du JSON
        if isa(data, Dict) && haskey(data, "parties_condensees")
            # Structure: {"parties_condensees": [...]}
            parties = data["parties_condensees"]
            println("✅ Données chargées: $(length(parties)) parties trouvées")
            return parties
        elseif isa(data, Vector)
            # Structure: [partie1, partie2, ...]
            println("✅ Données chargées: $(length(data)) parties trouvées")
            return data
        else
            if isa(data, Dict)
                println("❌ Structure JSON non reconnue. Clés disponibles: $(collect(keys(data)))")
            else
                println("❌ Structure JSON non reconnue. Liste non détectée")
            end
            return Dict{String, Any}[]
        end

    catch e
        if isa(e, SystemError)
            println("❌ Erreur: Fichier $filepath non trouvé")
        elseif isa(e, ArgumentError) || isa(e, JSON.ParserError)
            println("❌ Erreur JSON: $e")
        else
            println("❌ Erreur inattendue: $e")
        end
        return Dict{String, Any}[]
    end
end