#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour nettoyer les dossiers après consolidation
Supprime les fichiers sources et dossiers vides, ne garde que les fichiers consolidés
"""

import os
import shutil
from pathlib import Path

def clean_after_consolidation(base_directory):
    """
    Nettoie les dossiers après consolidation en supprimant les fichiers sources
    et les dossiers vides, ne gardant que les fichiers consolidés
    """
    base_path = Path(base_directory)
    
    if not base_path.exists():
        print(f"❌ Le dossier de base n'existe pas: {base_directory}")
        return
    
    print(f"🧹 NETTOYAGE APRÈS CONSOLIDATION")
    print("=" * 80)
    print(f"📁 Dossier de base: {base_directory}")
    
    # Vérifier qu'il y a des fichiers consolidés
    consolidated_files = list(base_path.glob("*_consolidated.txt"))
    if not consolidated_files:
        print("❌ Aucun fichier consolidé trouvé. Exécutez d'abord la consolidation.")
        return
    
    print(f"✅ {len(consolidated_files)} fichiers consolidés trouvés:")
    for consolidated_file in sorted(consolidated_files):
        file_size = consolidated_file.stat().st_size
        print(f"   - {consolidated_file.name} ({file_size:,} octets)")
    
    print("\n" + "=" * 80)
    
    # Obtenir tous les sous-dossiers (classes)
    class_directories = [d for d in base_path.iterdir() if d.is_dir()]
    
    if not class_directories:
        print("✅ Aucun dossier de classe à nettoyer")
        return
    
    print(f"🗂️  {len(class_directories)} dossiers de classe à traiter:")
    for class_dir in sorted(class_directories):
        txt_files = list(class_dir.glob("*.txt"))
        print(f"   - {class_dir.name} ({len(txt_files)} fichiers .txt)")
    
    # Demander confirmation
    print("\n⚠️  ATTENTION: Cette opération va DÉFINITIVEMENT supprimer:")
    print("   - Tous les fichiers .txt dans les dossiers de classes")
    print("   - Tous les dossiers de classes vides")
    print("   - Seuls les fichiers *_consolidated.txt seront conservés")
    
    response = input("\n🤔 Voulez-vous procéder au nettoyage? (oui/non): ").lower().strip()
    
    if response != 'oui':
        print("❌ Nettoyage annulé")
        return
    
    print("\n🧹 Début du nettoyage...")
    print("=" * 80)
    
    total_files_deleted = 0
    total_dirs_deleted = 0
    
    # Traiter chaque dossier de classe
    for class_dir in sorted(class_directories):
        class_name = class_dir.name
        print(f"\n🔄 Nettoyage du dossier: {class_name}")
        
        # Trouver tous les fichiers .txt dans ce dossier
        txt_files = list(class_dir.glob("*.txt"))
        
        if not txt_files:
            print(f"   📂 Dossier déjà vide")
        else:
            print(f"   🗑️  Suppression de {len(txt_files)} fichiers .txt...")
            files_deleted = 0
            
            for txt_file in txt_files:
                try:
                    txt_file.unlink()  # Supprimer le fichier
                    files_deleted += 1
                    print(f"   ✅ Supprimé: {txt_file.name}")
                except Exception as e:
                    print(f"   ❌ Erreur lors de la suppression de {txt_file.name}: {e}")
            
            total_files_deleted += files_deleted
            print(f"   📊 {files_deleted}/{len(txt_files)} fichiers supprimés")
        
        # Supprimer le dossier s'il est vide
        try:
            remaining_files = list(class_dir.iterdir())
            if not remaining_files:  # Dossier vide
                class_dir.rmdir()
                total_dirs_deleted += 1
                print(f"   🗂️  Dossier vide supprimé: {class_name}")
            else:
                print(f"   📁 Dossier conservé (contient {len(remaining_files)} éléments)")
        except Exception as e:
            print(f"   ❌ Erreur lors de la suppression du dossier {class_name}: {e}")
    
    # Résumé final
    print("\n" + "=" * 80)
    print("✅ NETTOYAGE TERMINÉ!")
    print("=" * 80)
    print(f"🗑️  {total_files_deleted} fichiers .txt supprimés")
    print(f"🗂️  {total_dirs_deleted} dossiers vides supprimés")
    print(f"📄 {len(consolidated_files)} fichiers consolidés conservés")
    
    # Lister le contenu final
    print(f"\n📋 Contenu final du dossier:")
    remaining_items = list(base_path.iterdir())
    for item in sorted(remaining_items):
        if item.is_file():
            file_size = item.stat().st_size
            print(f"   📄 {item.name} ({file_size:,} octets)")
        elif item.is_dir():
            sub_items = list(item.iterdir())
            print(f"   📁 {item.name}/ ({len(sub_items)} éléments)")

def main():
    """
    Point d'entrée principal
    """
    print("🧹 NETTOYEUR APRÈS CONSOLIDATION")
    print("=" * 80)
    
    # Dossier de base contenant les fichiers consolidés
    base_directory = r"C:\Users\<USER>\Desktop\11\entropie_baccarat_analyzer"
    
    # Vérifier que le dossier existe
    if not os.path.exists(base_directory):
        print(f"❌ Le dossier de base n'existe pas: {base_directory}")
        return
    
    # Nettoyer après consolidation
    clean_after_consolidation(base_directory)

if __name__ == "__main__":
    main()
