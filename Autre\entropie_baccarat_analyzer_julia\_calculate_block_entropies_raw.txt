# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 381 à 413
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _calculate_block_entropies_raw(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Vector{Float64}

Calcule l'entropie pour des blocs de différentes longueurs SANS normalisation.
NOUVELLE MÉTHODE: Pour le calcul correct de l'entropie métrique.

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
"""
function _calculate_block_entropies_raw(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Vector{Float64}
    if length(sequence) < 2
        return [0.0]
    end

    entropies = Float64[]

    for block_len in 1:min(max_length, length(sequence))
        # CORRECTION AEP: Créer toutes les sous-séquences de cette longueur
        block_sequences = Vector{String}[]
        for i in 1:(length(sequence) - block_len + 1)
            block_sequence = sequence[i:i+block_len-1]
            push!(block_sequences, block_sequence)
        end

        if isempty(block_sequences)
            push!(entropies, 0.0)
            continue
        end

        # CORRECTION AEP: Calculer l'entropie moyenne des blocs selon la formule AEP
        total_entropy = 0.0
        for block_seq in block_sequences
            total_entropy += _calculate_sequence_entropy_aep(analyzer, block_seq)
        end

        # Entropie moyenne des blocs de cette longueur
        block_entropy = !isempty(block_sequences) ? total_entropy / length(block_sequences) : 0.0
        push!(entropies, block_entropy)
    end

    return entropies
end