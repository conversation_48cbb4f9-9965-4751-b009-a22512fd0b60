# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 660 à 707
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _approximate_topological_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64

Approximation de l'entropie topologique respectant le principe variationnel.
CORRECTION EXPERTE: Assure h_top ≥ h_μ selon le principe variationnel.

Référence: entropie/cours_entropie/niveau_expert/02_entropie_topologique.md
Principe: h_top(T) = sup{h_μ(T) : μ T-invariante} ≥ h_μ(T)
"""
function _approximate_topological_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end

    # Compter les motifs uniques de longueurs croissantes
    max_length = min(5, length(sequence))
    pattern_counts = Int[]

    for length in 1:max_length
        patterns = Set{Tuple}()
        for i in 1:(length(sequence) - length + 1)
            pattern = Tuple(sequence[i:i+length-1])
            push!(patterns, pattern)
        end
        push!(pattern_counts, length(patterns))
    end

    if length(pattern_counts) < 2
        return 0.0
    end

    # CORRECTION EXPERTE: Estimation correcte respectant le principe variationnel
    # h_top = lim_{n→∞} (1/n) log(N(n)) où N(n) = nombre de motifs de longueur n
    # Formule correcte: (1/n) log(N(n))
    growth_rates = Float64[]
    for i in 1:length(pattern_counts)
        length_val = i  # Longueur du motif (commence à 1)
        if pattern_counts[i] > 0
            growth_rate = _safe_log(analyzer, [pattern_counts[i]])[1] / length_val
            push!(growth_rates, growth_rate)
        end
    end

    if !isempty(growth_rates)
        # Prendre le maximum pour respecter le principe variationnel
        h_top_estimate = maximum(growth_rates)
    else
        h_top_estimate = _safe_log(analyzer, [pattern_counts[end]])[1] / max_length
    end

    # CORRECTION: Assurer h_top ≥ h_μ (principe variationnel)
    # Calculer une estimation rapide de h_μ pour comparaison
    if length(sequence) >= 3
        h_metric_estimate = _estimate_metric_entropy(analyzer, sequence, 3)
        h_top_estimate = max(h_top_estimate, h_metric_estimate * 1.1)  # Marge de sécurité
    end

    return h_top_estimate
end