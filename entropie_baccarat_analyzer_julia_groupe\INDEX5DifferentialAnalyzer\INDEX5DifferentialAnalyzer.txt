# ============================================================
# CLASSE: INDEX5DifferentialAnalyzer
# Fichier consolidé généré automatiquement
# Nombre de fichiers sources: 3
# ============================================================

# --------------------------------------------------
# FICHIER SOURCE: __init___3.txt
# --------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2300 à 2301
# Type: Méthode de la classe INDEX5DifferentialAnalyzer
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

"""
    INDEX5DifferentialAnalyzer()

Initialisation de l'analyseur différentiel INDEX5
"""
mutable struct INDEX5DifferentialAnalyzer
    function INDEX5DifferentialAnalyzer()
        new()
    end
end


# --------------------------------------------------
# FICHIER SOURCE: calculate_differentials.txt
# --------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2303 à 2345
# Type: Méthode de la classe INDEX5DifferentialAnalyzer

"""
    calculate_differentials(analyzer::INDEX5DifferentialAnalyzer, entropy_evolution::Vector{Dict{String, Any}})::Vector{Dict{String, Any}}

Calcule les différentiels pour toutes les métriques.

# Arguments
- `entropy_evolution::Vector{Dict{String, Any}}`: Liste des résultats d'évolution entropique

# Returns
- `Vector{Dict{String, Any}}`: Liste des différentiels pour chaque main
"""
function calculate_differentials(analyzer::INDEX5DifferentialAnalyzer, entropy_evolution::Vector{Dict{String, Any}})::Vector{Dict{String, Any}}
    if isempty(entropy_evolution) || length(entropy_evolution) < 2
        return Dict{String, Any}[]
    end

    differentials = Dict{String, Any}[]

    # Première main : différentiels = 0 (pas de main précédente)
    push!(differentials, Dict{String, Any}(
        "position" => 1,
        "diff_conditional" => 0.0,
        "diff_entropy_rate" => 0.0,
        "diff_simple_entropy" => 0.0,
        "diff_simple_entropy_theoretical" => 0.0
    ))

    # Calculer les différentiels pour les mains suivantes
    for i in 2:length(entropy_evolution)
        current = entropy_evolution[i]
        previous = entropy_evolution[i-1]

        diff_conditional = abs(get(current, "conditional_entropy", 0) - get(previous, "conditional_entropy", 0))
        diff_entropy_rate = abs(get(current, "entropy_rate", 0) - get(previous, "entropy_rate", 0))
        diff_simple_entropy = abs(get(current, "simple_entropy", 0) - get(previous, "simple_entropy", 0))
        diff_simple_entropy_theoretical = abs(get(current, "simple_entropy_theoretical", 0) - get(previous, "simple_entropy_theoretical", 0))

        push!(differentials, Dict{String, Any}(
            "position" => get(current, "position", i),
            "diff_conditional" => diff_conditional,
            "diff_entropy_rate" => diff_entropy_rate,
            "diff_simple_entropy" => diff_simple_entropy,
            "diff_simple_entropy_theoretical" => diff_simple_entropy_theoretical
        ))
    end

    return differentials
end


# --------------------------------------------------
# FICHIER SOURCE: get_differential_statistics.txt
# --------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2347 à 2376
# Type: Méthode de la classe INDEX5DifferentialAnalyzer

"""
    get_differential_statistics(analyzer::INDEX5DifferentialAnalyzer, differentials::Vector{Dict{String, Any}})::Dict{String, Any}

Calcule les statistiques sur les différentiels.

# Arguments
- `differentials::Vector{Dict{String, Any}}`: Liste des différentiels calculés

# Returns
- `Dict{String, Any}`: Dictionnaire avec les statistiques
"""
function get_differential_statistics(analyzer::INDEX5DifferentialAnalyzer, differentials::Vector{Dict{String, Any}})::Dict{String, Any}
    if isempty(differentials) || length(differentials) < 2
        return Dict{String, Any}()
    end

    # Exclure la première main (différentiels = 0)
    valid_diffs = differentials[2:end]

    stats = Dict{String, Any}()

    for metric in ["diff_conditional", "diff_entropy_rate", "diff_simple_entropy", "diff_simple_entropy_theoretical"]
        values = [d[metric] for d in valid_diffs if haskey(d, metric)]

        if !isempty(values)
            mean_val = sum(values) / length(values)
            std_val = length(values) > 1 ? sqrt(sum((x - mean_val)^2 for x in values) / length(values)) : 0.0

            stats[metric] = Dict{String, Any}(
                "min" => minimum(values),
                "max" => maximum(values),
                "mean" => mean_val,
                "std" => std_val
            )
        end
    end

    return stats
end


# ==================================================
# FIN DE LA CLASSE INDEX5DifferentialAnalyzer
# TOTAL: 3 fichiers sources
# ==================================================
