# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 462 à 514
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _calculate_conditional_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64

Calcule l'entropie conditionnelle H(Xₙ|X₁,...,Xₙ₋₁) avec contexte fixe.
CORRECTION EXPERTE: Utilise un contexte de longueur fixe pour cohérence mathématique.

Référence: entropie/cours_entropie/niveau_debutant/03_entropie_conditionnelle.md
Formule: H(X|Y) = ∑ P(y) × H(X|y)
"""
function _calculate_conditional_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end

    # CORRECTION: Utiliser un contexte de longueur fixe (longueur 1 pour simplicité)
    # Cela donne H(Xₙ|Xₙ₋₁) au lieu de mélanger différentes longueurs
    context_length = min(1, length(sequence) - 1)

    # Compter les transitions contexte → symbole suivant
    context_transitions = Dict{Any, Dict{String, Int}}()

    for i in 1:(length(sequence) - context_length)
        if context_length == 1
            context = sequence[i]  # Contexte de longueur 1
        else
            context = Tuple(sequence[i:i+context_length-1])
        end

        next_symbol = sequence[i+context_length]

        if context ∉ keys(context_transitions)
            context_transitions[context] = Dict{String, Int}()
        end

        if next_symbol ∉ keys(context_transitions[context])
            context_transitions[context][next_symbol] = 0
        end
        context_transitions[context][next_symbol] += 1
    end

    if isempty(context_transitions)
        return 0.0
    end

    # Calculer H(X|Contexte) = ∑ P(contexte) × H(X|contexte)
    total_transitions = sum(sum(values(transitions)) for transitions in values(context_transitions))
    conditional_entropy = 0.0

    for (context, transitions) in context_transitions
        context_prob = sum(values(transitions)) / total_transitions

        # CORRECTION AEP: H(X|ce contexte) calculé selon AEP
        # Créer la séquence des symboles suivants pour ce contexte
        context_sequence = String[]
        for (next_symbol, count) in transitions
            append!(context_sequence, repeat([next_symbol], count))
        end

        context_entropy = _calculate_sequence_entropy_aep(analyzer, context_sequence)
        conditional_entropy += context_prob * context_entropy
    end

    return conditional_entropy
end