# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1369 à 1398
# Type: Méthode de la classe INDEX5Calculator

    def calculate_bayesian_divergence_score(self, sequence_history):
        """
        Calcule la divergence entre les probabilités observées et théoriques INDEX5
        en utilisant la divergence de Kullback-Leibler. Plus le score est élevé,
        plus la séquence s'écarte des probabilités théoriques.
        """
        if len(sequence_history) < 10:
            return 0.0

        from collections import Counter

        # 1. Calculer les fréquences observées
        observed_counts = Counter(sequence_history)
        total_observations = len(sequence_history)

        # 2. Calculer la divergence KL: D_KL(P_obs || P_theo) = Σ P_obs(x) log(P_obs(x) / P_theo(x))
        kl_divergence = 0.0

        for index5_value in self.THEORETICAL_PROBS:
            p_theoretical = self.THEORETICAL_PROBS[index5_value]
            p_observed = observed_counts.get(index5_value, 0) / total_observations

            if p_observed > 0:  # Éviter log(0)
                kl_divergence += p_observed * np.log2(p_observed / p_theoretical)

        # 3. Normaliser le score (la divergence KL peut être très élevée)
        # Utiliser une fonction sigmoïde pour normaliser entre 0 et 1
        normalized_score = 2 / (1 + np.exp(-kl_divergence)) - 1

        return round(max(0.0, normalized_score), 4)