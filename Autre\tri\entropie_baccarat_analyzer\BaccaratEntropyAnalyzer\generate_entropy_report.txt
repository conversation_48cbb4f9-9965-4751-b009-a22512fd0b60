# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 771 à 1072
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    generate_entropy_report(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any})::String

Génère un rapport détaillé d'analyse d'entropie selon les méthodes avancées.

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md

# Arguments
- `analysis_result::Dict{String, Any}`: Résultats d'analyse d'une partie

# Returns
- `String`: Rapport formaté en texte avec métriques avancées
"""
function generate_entropy_report(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any})::String
    if !haskey(analysis_result, "entropy_evolution")
        return "❌ Pas de données d'analyse disponibles"
    end

    evolution = analysis_result["entropy_evolution"]
    game_id = analysis_result["game_id"]
    sequence_length = analysis_result["sequence_length"]
    complexity = get(analysis_result, "complexity_metrics", Dict{String, Any}())

    # Statistiques des nouvelles métriques
    metric_entropies = [item["metric_entropy"] for item in evolution]
    conditional_entropies = [item["conditional_entropy"] for item in evolution]
    entropy_rates = [item["entropy_rate"] for item in evolution]
    simple_entropies = [item["simple_entropy"] for item in evolution]  # Ancienne méthode

    # Valeurs finales
    final_metric = get(analysis_result, "final_metric_entropy", 0)
    final_conditional = get(analysis_result, "final_conditional_entropy", 0)
    final_rate = get(analysis_result, "final_entropy_rate", 0)
    final_simple = get(analysis_result, "final_simple_entropy", 0)

    # Positions des maxima
    max_metric_pos = get(analysis_result, "max_metric_entropy_position", 0)
    max_conditional_pos = get(analysis_result, "max_conditional_entropy_position", 0)

    report = """
🎯 RAPPORT D'ANALYSE D'ENTROPIE AVANCÉE - INDEX5
===============================================
Méthodes: Kolmogorov-Sinai, Entropie de Blocs, Entropie Conditionnelle
Référence: entropie/cours_entropie/niveau_expert/

📊 INFORMATIONS GÉNÉRALES
Partie ID: $game_id
Longueur de la séquence: $sequence_length mains
Entropie théorique maximale: $(round(analyzer.theoretical_entropy, digits=4)) bits

📈 MÉTRIQUES D'ENTROPIE AVANCÉES
┌─ Entropie Métrique (Kolmogorov-Sinai) ─┐
│ Finale: $(round(final_metric, digits=4)) bits/symbole │
│ Maximum: $(round(maximum(metric_entropies), digits=4)) bits (position $max_metric_pos) │
│ Moyenne: $(round(mean(metric_entropies), digits=4)) bits │
└────────────────────────────────────────┘

┌─ Entropie Conditionnelle H(Xₙ|X₁...Xₙ₋₁) ─┐
│ Finale: $(round(final_conditional, digits=4)) bits │
│ Maximum: $(round(maximum(conditional_entropies), digits=4)) bits (position $max_conditional_pos) │
│ Moyenne: $(round(mean(conditional_entropies), digits=4)) bits │
│ → Mesure la prédictibilité du prochain symbole │
└─────────────────────────────────────────────┘

┌─ Taux d'Entropie (Entropy Rate) ─┐
│ Final: $(round(final_rate, digits=4)) bits/symbole │
│ → Limite asymptotique de l'information par symbole │
└──────────────────────────────────┘

📊 COMPARAISON DES MÉTHODES D'ENTROPIE
Entropie simple (fréquences observées): $(round(final_simple, digits=4)) bits
Entropie simple (probabilités théoriques): $(round(get(evolution[end], "simple_entropy_theoretical", 0), digits=4)) bits
Entropie métrique (Kolmogorov-Sinai): $(round(final_metric, digits=4)) bits
Différence observée vs métrique: $(round(abs(final_simple - final_metric), digits=4)) bits
Différence théorique vs métrique: $(round(abs(get(evolution[end], "simple_entropy_theoretical", 0) - final_metric), digits=4)) bits

🔬 ANALYSE DE COMPLEXITÉ
"""

    # Ajout des métriques de complexité
    if !isempty(complexity)
        report *= """Complexité Lempel-Ziv: $(get(complexity, "lz_complexity", "N/A"))
Entropie Topologique: $(round(get(complexity, "topological_entropy", 0), digits=4)) bits
Diversité relative: $(round(get(complexity, "sequence_diversity", 0)*100, digits=1))%
Taux de répétition: $(round(get(complexity, "repetition_rate", 0)*100, digits=1))%

🎲 MOTIFS UNIQUES OBSERVÉS
"""
        unique_patterns = get(complexity, "unique_patterns", Dict{String, Any}())
        for (length, count) in unique_patterns
            length_num = split(length, "_")[2]
            report *= "Longueur $length_num: $count motifs uniques\n"
        end
    end

    # Initialiser le calculateur, prédicteur, validateur et analyseur différentiel INDEX5
    # CORRECTION: Passer l'instance analyzer pour accéder à _calculate_sequence_entropy_aep
    calculator = INDEX5Calculator(analyzer)
    predictor = INDEX5Predictor()
    validator = INDEX5PredictionValidator()
    differential_analyzer = INDEX5DifferentialAnalyzer()

    # Calculer les différentiels
    differentials = calculate_differentials(differential_analyzer, evolution)

    report *= """
📋 ÉVOLUTION COMPLÈTE - TOUTES LES $(length(evolution)) MAINS AVEC MÉTRIQUES ET PRÉDICTIONS INDEX5
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | Uniques | Métriques INDEX5 | Prédiction POUR cette main
---------|-------------|----------|----------------|------|-------------|--------------|---------|------------------|---------------------------
"""

    # Pré-calculer toutes les prédictions pour éviter le décalage
    sequence = get(analysis_result, "full_sequence", String[])
    predictions = ["N/A"]  # Première main n'a pas de prédiction

    for i in 1:(length(evolution) - 1)
        item = evolution[i]
        sequence_up_to_i = i <= length(sequence) ? sequence[1:i] : sequence
        current_metrics = Dict{String, Any}(
            "conditional_entropy" => get(item, "conditional_entropy", 0),
            "metric_entropy" => get(item, "metric_entropy", 0),
            "repetition_rate" => get(item, "entropy_rate", 0),
            "predictability_score" => 1 - (get(item, "conditional_entropy", 3.9309) / 3.9309)
        )

        # Calculer la prédiction pour la main suivante (i+1)
        if length(sequence_up_to_i) >= 5  # Minimum requis pour prédiction
            prediction_result = predict_next_index5(predictor, sequence_up_to_i, current_metrics)
            if prediction_result !== nothing && isa(prediction_result, Dict)
                predicted_value = get(prediction_result, "predicted_index5", "N/A")
                confidence = get(prediction_result, "confidence", 0)

                # Gestion spéciale pour WAIT
                if predicted_value == "WAIT"
                    prediction_display = "WAIT"
                else
                    prediction_display = "$predicted_value($(round(confidence, digits=2)))"
                end
            else
                # Essayer prédiction contextuelle simple
                simple_pred = predict_context_level(predictor, sequence_up_to_i, current_metrics)
                prediction_display = simple_pred !== nothing ? simple_pred : "N/A"
            end
        else
            prediction_display = "N/A"
        end

        push!(predictions, prediction_display)

        # Valider la prédiction avec la valeur réelle suivante
        if prediction_display != "N/A"
            next_actual_index5 = sequence[i + 1]
            validate_prediction(validator, prediction_display, next_actual_index5, item["position"] + 1)
        end
    end

    # Générer le rapport avec les prédictions correctement alignées
    for (i, item) in enumerate(evolution)
        simple_theo = get(item, "simple_entropy_theoretical", 0)
        index5_value = i <= length(sequence) ? sequence[i] : "N/A"

        # Calculer les métriques INDEX5 pour cette position
        sequence_up_to_i = i <= length(sequence) ? sequence[1:i] : sequence
        current_metrics = Dict{String, Any}(
            "conditional_entropy" => get(item, "conditional_entropy", 0),
            "metric_entropy" => get(item, "metric_entropy", 0),
            "repetition_rate" => get(item, "entropy_rate", 0),
            "predictability_score" => 1 - (get(item, "conditional_entropy", 3.9309) / 3.9309)
        )

        # Calculer les nouvelles métriques INDEX5
        if length(sequence_up_to_i) >= 2
            index5_metrics = calculate_all_metrics(calculator, sequence_up_to_i, current_metrics, evolution[1:i])

            # Sélectionner les 3 métriques les plus importantes pour l'affichage
            context_pred = get(index5_metrics, "context_predictability", 0)
            pattern_str = get(index5_metrics, "pattern_strength", 0)
            consensus = get(index5_metrics, "multi_algorithm_consensus", 0)

            metrics_display = @sprintf("Ctx:%.3f Pat:%.3f Con:%.3f", context_pred, pattern_str, consensus)
        else
            metrics_display = "Ctx:0.000 Pat:0.000 Con:0.000"
        end

        # Utiliser la prédiction pré-calculée pour cette main
        prediction_display = i <= length(predictions) ? predictions[i] : "N/A"

        report *= @sprintf("Main %2d  | %11s | %6.3f  | %12.3f  | %4.3f | %9.3f | %10.3f | %2d/18 | %s | %14s\n",
            item["position"], index5_value, item["metric_entropy"], item["conditional_entropy"],
            item["entropy_rate"], item["simple_entropy"], simple_theo, item["unique_values"],
            metrics_display, prediction_display)
    end

    # Ajouter le nouveau tableau avec les différentiels
    report *= """

📊 TABLEAU AVEC DIFFÉRENTIELS - ANALYSE DES VARIATIONS ENTRE MAINS
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | DiffCond | DiffTaux | DiffDivEntropG | DiffEntropG | Prédiction
---------|-------------|----------|----------------|------|-------------|--------------|----------|----------|----------------|-------------|------------
"""

    # Générer le tableau avec différentiels
    for (i, item) in enumerate(evolution)
        simple_theo = get(item, "simple_entropy_theoretical", 0)
        index5_value = i <= length(sequence) ? sequence[i] : "N/A"
        prediction_display = i <= length(predictions) ? predictions[i] : "N/A"

        # Récupérer les différentiels correspondants
        if i <= length(differentials)
            diff_data = differentials[i]
            diff_cond = get(diff_data, "diff_conditional", 0)
            diff_taux = get(diff_data, "diff_entropy_rate", 0)
            diff_div_entrop = get(diff_data, "diff_simple_entropy", 0)
            diff_entrop = get(diff_data, "diff_simple_entropy_theoretical", 0)
        else
            diff_cond = diff_taux = diff_div_entrop = diff_entrop = 0.0
        end

        report *= @sprintf("Main %2d  | %11s | %6.3f  | %12.3f  | %4.3f | %9.3f | %10.3f | %6.3f   | %6.3f   | %12.3f   | %9.3f   | %14s\n",
            item["position"], index5_value, item["metric_entropy"], item["conditional_entropy"],
            item["entropy_rate"], item["simple_entropy"], simple_theo, diff_cond, diff_taux,
            diff_div_entrop, diff_entrop, prediction_display)
    end

    # Ajouter le nouveau tableau prédictif avec différentiels
    predictive_table_generator = INDEX5PredictiveDifferentialTable()
    predictive_table = generate_predictive_table(
        predictive_table_generator,
        analysis_result["full_sequence"],
        analysis_result["entropy_evolution"],
        analyzer
    )

    # CORRECTION : Injecter directement les données calculées dans le tableau SCORES
    computed_differentials = predictive_table_generator._differential_cache
    predictive_score_table_generator = INDEX5PredictiveScoreTable()
    predictive_score_table = generate_predictive_score_table(
        predictive_score_table_generator,
        analysis_result["full_sequence"],
        analysis_result["entropy_evolution"],
        analyzer,
        computed_differentials
    )

    report *= """

🔮 TABLEAU PRÉDICTIF - DIFFÉRENTIELS POUR LES 9 VALEURS INDEX5 POSSIBLES
═══════════════════════════════════════════════════════════════════════

$predictive_table

$predictive_score_table
"""

    # Ajouter les statistiques des différentiels
    diff_stats = get_differential_statistics(differential_analyzer, differentials)
    if !isempty(diff_stats)
        diff_cond_stats = get(diff_stats, "diff_conditional", Dict{String, Any}())
        diff_rate_stats = get(diff_stats, "diff_entropy_rate", Dict{String, Any}())
        diff_simple_stats = get(diff_stats, "diff_simple_entropy", Dict{String, Any}())
        diff_theo_stats = get(diff_stats, "diff_simple_entropy_theoretical", Dict{String, Any}())

        report *= """

📈 STATISTIQUES DES DIFFÉRENTIELS
═══════════════════════════════

🔢 DIFFÉRENTIELS ENTROPIE CONDITIONNELLE (DiffCond)
• Minimum: $(round(get(diff_cond_stats, "min", 0), digits=4)) bits
• Maximum: $(round(get(diff_cond_stats, "max", 0), digits=4)) bits
• Moyenne: $(round(get(diff_cond_stats, "mean", 0), digits=4)) bits
• Écart-type: $(round(get(diff_cond_stats, "std", 0), digits=4)) bits

🔢 DIFFÉRENTIELS TAUX D'ENTROPIE (DiffTaux)
• Minimum: $(round(get(diff_rate_stats, "min", 0), digits=4)) bits
• Maximum: $(round(get(diff_rate_stats, "max", 0), digits=4)) bits
• Moyenne: $(round(get(diff_rate_stats, "mean", 0), digits=4)) bits
• Écart-type: $(round(get(diff_rate_stats, "std", 0), digits=4)) bits

🔢 DIFFÉRENTIELS DIVERSITÉ ENTROPIQUE (DiffDivEntropG)
• Minimum: $(round(get(diff_simple_stats, "min", 0), digits=4)) bits
• Maximum: $(round(get(diff_simple_stats, "max", 0), digits=4)) bits
• Moyenne: $(round(get(diff_simple_stats, "mean", 0), digits=4)) bits
• Écart-type: $(round(get(diff_simple_stats, "std", 0), digits=4)) bits

🔢 DIFFÉRENTIELS ENTROPIE GÉNÉRALE (DiffEntropG)
• Minimum: $(round(get(diff_theo_stats, "min", 0), digits=4)) bits
• Maximum: $(round(get(diff_theo_stats, "max", 0), digits=4)) bits
• Moyenne: $(round(get(diff_theo_stats, "mean", 0), digits=4)) bits
• Écart-type: $(round(get(diff_theo_stats, "std", 0), digits=4)) bits
"""
    end

    # Ajout d'analyses statistiques détaillées
    min_metric_idx = argmin(metric_entropies)
    max_metric_idx = argmax(metric_entropies)
    min_cond_idx = argmin(conditional_entropies)
    max_cond_idx = argmax(conditional_entropies)

    report *= """
📊 ANALYSES STATISTIQUES COMPLÈTES
═══════════════════════════════════

🔢 STATISTIQUES D'ENTROPIE MÉTRIQUE
• Minimum: $(round(minimum(metric_entropies), digits=4)) bits (main $min_metric_idx)
• Maximum: $(round(maximum(metric_entropies), digits=4)) bits (main $max_metric_idx)
• Écart-type: $(round(std(metric_entropies), digits=4)) bits
• Coefficient de variation: $(round(std(metric_entropies)/mean(metric_entropies)*100, digits=1))%

🔢 STATISTIQUES D'ENTROPIE CONDITIONNELLE
• Minimum: $(round(minimum(conditional_entropies), digits=4)) bits (main $min_cond_idx)
• Maximum: $(round(maximum(conditional_entropies), digits=4)) bits (main $max_cond_idx)
• Écart-type: $(round(std(conditional_entropies), digits=4)) bits
• Coefficient de variation: $(round(std(conditional_entropies)/mean(conditional_entropies)*100, digits=1))%

🔢 ÉVOLUTION DE LA DIVERSITÉ
• Diversité initiale: $(evolution[1]["unique_values"])/18 ($(round(evolution[1]["unique_values"]/18*100, digits=1))%)
• Diversité finale: $(evolution[end]["unique_values"])/18 ($(round(evolution[end]["unique_values"]/18*100, digits=1))%)
• Croissance de diversité: +$(evolution[end]["unique_values"] - evolution[1]["unique_values"]) valeurs uniques

🎯 POINTS D'INTÉRÊT IDENTIFIÉS
• Main avec entropie métrique maximale: $max_metric_pos ($(round(maximum(metric_entropies), digits=4)) bits)
• Main avec entropie conditionnelle maximale: $max_conditional_pos ($(round(maximum(conditional_entropies), digits=4)) bits)
• Stabilisation de l'entropie métrique: $(length(metric_entropies) >= 10 && std(metric_entropies[end-9:end]) < 0.05 ? "Oui" : "Non") (10 dernières mains)

🔍 INTERPRÉTATION AVANCÉE
• Entropie métrique moyenne ($(round(mean(metric_entropies), digits=3)) bits) = $(round(mean(metric_entropies)/analyzer.theoretical_entropy*100, digits=1))% du maximum théorique
• Entropie conditionnelle faible → Forte dépendance temporelle, patterns récurrents exploitables
• Taux d'entropie stable → Information moyenne générée par symbole à long terme
• Complexité LZ ($(get(complexity, "lz_complexity", "N/A"))) → Séquence $(get(complexity, "lz_complexity", 60) < 40 ? "hautement" : "modérément") compressible
• Coefficient de variation faible → Comportement $(std(metric_entropies)/mean(metric_entropies) < 0.2 ? "stable" : "variable")

$(get_detailed_report(validator))
"""

    return report
end