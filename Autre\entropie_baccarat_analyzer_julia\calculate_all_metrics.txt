# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1669 à 1716
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_all_metrics(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any}, entropy_evolution::Vector{Dict{String, Any}})::Dict{String, Any}

Calcule toutes les métriques disponibles pour une position donnée.
Retourne un dictionnaire avec tous les scores calculés.
"""
function calculate_all_metrics(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any}, entropy_evolution::Vector{Dict{String, Any}})::Dict{String, Any}
    if length(sequence_history) < 2
        return Dict{String, Any}()
    end

    metrics = Dict{String, Any}()

    # 1. Prédictibilité contextuelle
    metrics["context_predictability"] = calculate_context_predictability(calculator, sequence_history, current_metrics)

    # 2. Force des patterns
    metrics["pattern_strength"] = calculate_pattern_strength(calculator, sequence_history)

    # 3. Score de stabilité entropique
    if !isempty(entropy_evolution)
        metrics["entropy_stability"] = calculate_entropy_stability_score(calculator, entropy_evolution)
    end

    # 4. Score de compression
    metrics["compression_score"] = calculate_compression_score(calculator, sequence_history, current_metrics)

    # 5. Richesse structurelle
    metrics["structural_richness"] = calculate_structural_richness_score(calculator, sequence_history, current_metrics)

    # 6. Divergence bayésienne
    metrics["bayesian_divergence"] = calculate_bayesian_divergence_score(calculator, sequence_history)

    # 7. Entropie conditionnelle contextuelle
    metrics["conditional_entropy_context"] = calculate_conditional_entropy_context(calculator, sequence_history)

    # 8. Consensus multi-algorithmes
    metrics["multi_algorithm_consensus"] = calculate_multi_algorithm_consensus_score(calculator, sequence_history, current_metrics)

    # 9. Score de patterns déterministes
    metrics["deterministic_pattern_score"] = calculate_deterministic_pattern_score(calculator, sequence_history)

    # 10. Alignement bayésien théorique
    metrics["bayesian_theoretical_alignment"] = calculate_bayesian_theoretical_alignment(calculator, sequence_history)

    # 11. Entropie de la matrice de transitions
    metrics["transition_matrix_entropy"] = calculate_transition_matrix_entropy(calculator, sequence_history)

    # 12. Stabilité des fréquences
    metrics["frequency_stability"] = calculate_frequency_stability_score(calculator, sequence_history)

    return metrics
end