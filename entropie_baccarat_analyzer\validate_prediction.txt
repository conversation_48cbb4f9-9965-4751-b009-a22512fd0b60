# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3082 à 3134
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    validate_prediction(validator::INDEX5PredictionValidator, predicted_index5::String, actual_index5::String, position::Int)::Union{Bool, Nothing}

Valide une prédiction en comparant les INDEX3
Ignore les prédictions "WAIT"
"""
function validate_prediction(validator::INDEX5PredictionValidator, predicted_index5::String, actual_index5::String, position::Int)::Union{Bool, Nothing}
    # Ignorer les prédictions WAIT
    if predicted_index5 == "WAIT"
        return nothing
    end

    # Extraire INDEX3 de la prédiction (le nettoyage est fait dans extract_index3)
    predicted_index3 = extract_index3(validator, predicted_index5)

    # Extraire INDEX3 de la valeur réelle
    actual_index3 = extract_index3(validator, actual_index5)

    # Vérifier si les deux INDEX3 sont valides
    if predicted_index3 !== nothing && actual_index3 !== nothing
        # RÈGLE TIE: Si réalité = TIE et prédiction ≠ TIE → NE PAS COMPTER
        if actual_index3 == "TIE" && predicted_index3 != "TIE"
            # Ne pas compter cette prédiction (ni valide ni invalide)
            return nothing
        end

        # Extraire le score de confiance
        confidence = extract_confidence(validator, predicted_index5)

        validator.total_predictions += 1
        is_correct = predicted_index3 == actual_index3

        if is_correct
            validator.correct_predictions += 1
        end

        # Compteur spécial pour poids pondéré >= 60% (seuil haute confiance)
        # RESTAURATION: Seuil basé sur poids pondéré cumulé comme dans l'ancien code
        if confidence >= 0.60
            validator.total_predictions_high_confidence += 1
            if is_correct
                validator.correct_predictions_high_confidence += 1
            end
        end

        # Enregistrer les détails
        push!(validator.prediction_details, Dict{String, Any}(
            "position" => position,
            "predicted_index5" => predicted_index5,
            "actual_index5" => actual_index5,
            "predicted_index3" => predicted_index3,
            "actual_index3" => actual_index3,
            "confidence" => confidence,
            "is_correct" => is_correct,
            "is_high_confidence" => confidence >= 0.60
        ))

        return is_correct
    end

    return nothing  # Prédiction non validable
end