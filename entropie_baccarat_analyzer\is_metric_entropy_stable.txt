# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1848 à 1858
# Type: Méthode de la classe INDEX5Predictor

    def is_metric_entropy_stable(self, recent_entropy_evolution):
        """
        Vérifie si l'entropie métrique est stable
        """
        if len(recent_entropy_evolution) < 5:
            return False

        metric_entropies = [item.get('metric_entropy', 0) for item in recent_entropy_evolution]
        variance = np.var(metric_entropies) if len(metric_entropies) > 0 else 0

        return variance < 0.1  # Seuil de stabilité