"""
BaccaratEntropyAnalyzer.jl

Package principal pour l'analyse d'entropie du baccarat INDEX5.
Ce package contient tous les modules nécessaires pour l'analyse entropique
complète des parties de baccarat.

Modules inclus:
- BaccaratEntropyAnalyzer: Module de base pour les calculs d'entropie
- INDEX5Calculator: Calculateur de métriques INDEX5
- INDEX5DifferentialAnalyzer: Analyseur des différentiels
- INDEX5PredictionValidator: Validateur de prédictions
- INDEX5PredictiveDifferentialTable: Tableaux différentiels prédictifs
- INDEX5PredictiveScoreCalculator: Calculateur de scores prédictifs
- INDEX5PredictiveScoreTable: Tableaux de scores prédictifs
- INDEX5Predictor: Prédicteur INDEX5 principal
- General: Interface utilisateur et point d'entrée principal

Usage:
```julia
using BaccaratEntropyAnalyzer
main()  # Lance l'interface interactive
```
"""
module BaccaratEntropyAnalyzer

# Chargement des modules dans l'ordre des dépendances
include("BaccaratEntropyAnalyzer/BaccaratEntropyAnalyzer.jl")
include("INDEX5Calculator/INDEX5Calculator.jl")
include("INDEX5DifferentialAnalyzer/INDEX5DifferentialAnalyzer.jl")
include("INDEX5PredictionValidator/INDEX5PredictionValidator.jl")
include("INDEX5PredictiveDifferentialTable/INDEX5PredictiveDifferentialTable.jl")
include("INDEX5PredictiveScoreCalculator/INDEX5PredictiveScoreCalculator.jl")
include("INDEX5PredictiveScoreTable/INDEX5PredictiveScoreTable.jl")
include("INDEX5Predictor/INDEX5Predictor.jl")
include("General/General.jl")

# Réexportation des fonctions principales
using .General
export main

# Réexportation des modules pour usage avancé
export BaccaratEntropyAnalyzer as BEA
export INDEX5Calculator as I5C
export INDEX5DifferentialAnalyzer as I5DA
export INDEX5PredictionValidator as I5PV
export INDEX5PredictiveDifferentialTable as I5PDT
export INDEX5PredictiveScoreCalculator as I5PSC
export INDEX5PredictiveScoreTable as I5PST
export INDEX5Predictor as I5P
export General

end # module BaccaratEntropyAnalyzer
