# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 709 à 722
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _calculate_repetition_rate(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64

Calcule le taux de répétition dans la séquence.
"""
function _calculate_repetition_rate(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64
    if length(sequence) < 2
        return 0.0
    end

    # Compter les répétitions immédiates
    repetitions = 0
    for i in 1:(length(sequence) - 1)
        if sequence[i] == sequence[i + 1]
            repetitions += 1
        end
    end

    return repetitions / (length(sequence) - 1)
end