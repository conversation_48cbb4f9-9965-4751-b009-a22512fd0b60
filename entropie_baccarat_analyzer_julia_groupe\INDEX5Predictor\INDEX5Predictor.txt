"""
    INDEX5Predictor()

Initialisation du prédicteur INDEX5
"""
mutable struct INDEX5Predictor
    THEORETICAL_PROBS::Dict{String, Float64}

    function INDEX5Predictor()
        # Probabilités théoriques INDEX5
        THEORETICAL_PROBS = Dict{String, Float64}(
            "0_A_BANKER" => 0.085136, "1_A_BANKER" => 0.086389,
            "0_B_BANKER" => 0.064676, "1_B_BANKER" => 0.065479,
            "0_C_BANKER" => 0.077903, "1_C_BANKER" => 0.078929,
            "0_A_PLAYER" => 0.085240, "1_A_PLAYER" => 0.086361,
            "0_B_PLAYER" => 0.076907, "1_B_PLAYER" => 0.077888,
            "0_C_PLAYER" => 0.059617, "1_C_PLAYER" => 0.060352,
            "0_A_TIE" => 0.017719, "1_A_TIE" => 0.017978,
            "0_B_TIE" => 0.016281, "1_B_TIE" => 0.016482,
            "0_C_TIE" => 0.013241, "1_C_TIE" => 0.013423
        )

        new(THEORETICAL_PROBS)
    end
end


"""
    apply_index1_constraint(predictor::INDEX5Predictor, current_index5::String, predicted_index5::String)::String

Applique la contrainte INDEX1 déterministe à la prédiction
Règles INDEX1:
- Si INDEX2=C: INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2=A ou B: INDEX1 se conserve (0→0, 1→1)
"""
function apply_index1_constraint(predictor::INDEX5Predictor, current_index5::String, predicted_index5::String)::String
    if isempty(current_index5) || isempty(predicted_index5)
        return predicted_index5
    end

    try
        # Extraire INDEX1 et INDEX2 actuels
        current_parts = split(current_index5, '_')
        current_index1 = parse(Int, current_parts[1])
        current_index2 = current_parts[2]

        # Calculer INDEX1 obligatoire pour n+1 selon les règles déterministes
        if current_index2 == "C"
            required_index1 = 1 - current_index1  # Inversion obligatoire
        else  # A ou B
            required_index1 = current_index1      # Conservation obligatoire
        end

        # Extraire INDEX2 et INDEX3 de la prédiction
        predicted_parts = split(predicted_index5, '_')
        if length(predicted_parts) >= 3
            predicted_index2 = predicted_parts[2]
            predicted_index3 = predicted_parts[3]

            # Construire INDEX5 contraint avec INDEX1 déterministe
            constrained_index5 = "$(required_index1)_$(predicted_index2)_$(predicted_index3)"
            return constrained_index5
        end

    catch e
        # En cas d'erreur, retourner la prédiction originale
        if isa(e, BoundsError) || isa(e, ArgumentError)
            # Continue to return original prediction
        else
            rethrow(e)
        end
    end

    return predicted_index5
end


"""
    calculate_conditional_probabilities(predictor::INDEX5Predictor, sequence_history::Vector{String})::Dict{String, Float64}

Calcule les probabilités conditionnelles observées
"""
function calculate_conditional_probabilities(predictor::INDEX5Predictor, sequence_history::Vector{String})::Dict{String, Float64}
    # Analyser les transitions depuis les 3 dernières valeurs
    context_transitions = Dict{Tuple, Dict{String, Int}}()

    for i in 4:length(sequence_history)
        context = Tuple(sequence_history[i-3:i-1])
        next_value = sequence_history[i]

        if context ∉ keys(context_transitions)
            context_transitions[context] = Dict{String, Int}()
        end

        if next_value ∉ keys(context_transitions[context])
            context_transitions[context][next_value] = 0
        end
        context_transitions[context][next_value] += 1
    end

    # Calculer probabilités conditionnelles pour le contexte actuel
    if length(sequence_history) >= 3
        current_context = Tuple(sequence_history[end-2:end])

        if haskey(context_transitions, current_context)
            transitions = context_transitions[current_context]
            total_transitions = sum(values(transitions))

            conditional_probs = Dict{String, Float64}()
            for (value, count) in transitions
                conditional_probs[value] = count / total_transitions
            end

            return conditional_probs
        end
    end

    return Dict{String, Float64}()
end


"""
    calculate_required_index1(predictor::INDEX5Predictor, current_index5::String)::Union{Int, Nothing}

Calcule INDEX1 obligatoire selon les règles déterministes
Règles INDEX1:
- Si INDEX2=C: INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2=A ou B: INDEX1 se conserve (0→0, 1→1)
"""
function calculate_required_index1(predictor::INDEX5Predictor, current_index5::String)::Union{Int, Nothing}
    if isempty(current_index5)
        return nothing
    end

    try
        current_parts = split(current_index5, '_')
        current_index1 = parse(Int, current_parts[1])
        current_index2 = current_parts[2]

        if current_index2 == "C"
            return 1 - current_index1  # Inversion obligatoire
        else  # A ou B
            return current_index1      # Conservation obligatoire
        end
    catch
        return nothing
    end
end


"""
    filter_prediction_by_constraint(predictor::INDEX5Predictor, prediction::Union{String, Nothing}, valid_values::Vector{String})::Union{String, Nothing}

Filtre une prédiction selon les contraintes INDEX1
Retourne la prédiction si valide, Nothing sinon
"""
function filter_prediction_by_constraint(predictor::INDEX5Predictor, prediction::Union{String, Nothing}, valid_values::Vector{String})::Union{String, Nothing}
    if prediction !== nothing && prediction in valid_values
        return prediction
    end
    return nothing
end


"""
    find_exact_pattern_continuation(predictor::INDEX5Predictor, pattern::Vector{String}, sequence_history::Vector{String})::Union{String, Nothing}

Trouve les continuations d'un pattern exact dans l'historique
"""
function find_exact_pattern_continuation(predictor::INDEX5Predictor, pattern::Vector{String}, sequence_history::Vector{String})::Union{String, Nothing}
    continuations = Dict{String, Int}()
    pattern_len = length(pattern)

    for i in 1:(length(sequence_history) - pattern_len)
        if sequence_history[i:i+pattern_len-1] == pattern
            # Si il y a une continuation après ce pattern
            if i + pattern_len <= length(sequence_history)
                next_value = sequence_history[i + pattern_len]
                continuations[next_value] = get(continuations, next_value, 0) + 1
            end
        end
    end

    if !isempty(continuations)
        # Retourner la continuation la plus fréquente
        best_continuation = argmax(continuations)
        return best_continuation
    end

    return nothing
end


"""
    find_pattern_continuations(predictor::INDEX5Predictor, pattern::Vector{String}, sequence_history::Vector{String})::Dict{String, Int}

Trouve toutes les continuations d'un pattern dans l'historique
"""
function find_pattern_continuations(predictor::INDEX5Predictor, pattern::Vector{String}, sequence_history::Vector{String})::Dict{String, Int}
    continuations = Dict{String, Int}()
    pattern_len = length(pattern)

    for i in 1:(length(sequence_history) - pattern_len)
        if sequence_history[i:i+pattern_len-1] == pattern
            # Si il y a une continuation après ce pattern
            if i + pattern_len <= length(sequence_history)
                next_value = sequence_history[i + pattern_len]
                continuations[next_value] = get(continuations, next_value, 0) + 1
            end
        end
    end

    return continuations
end


"""
    get_valid_index5_values(predictor::INDEX5Predictor, required_index1::Union{Int, Nothing})::Vector{String}

Retourne tous les INDEX5 avec INDEX1 obligatoire
"""
function get_valid_index5_values(predictor::INDEX5Predictor, required_index1::Union{Int, Nothing})::Vector{String}
    if required_index1 === nothing
        return String[]
    end

    valid_values = String[]
    for index2 in ["A", "B", "C"]
        for index3 in ["BANKER", "PLAYER", "TIE"]
            push!(valid_values, "$(required_index1)_$(index2)_$(index3)")
        end
    end
    return valid_values
end


"""
    is_metric_entropy_stable(predictor::INDEX5Predictor, recent_entropy_evolution::Vector{Dict{String, Any}})::Bool

Vérifie si l'entropie métrique est stable
"""
function is_metric_entropy_stable(predictor::INDEX5Predictor, recent_entropy_evolution::Vector{Dict{String, Any}})::Bool
    if length(recent_entropy_evolution) < 5
        return false
    end

    metric_entropies = [get(item, "metric_entropy", 0) for item in recent_entropy_evolution]
    variance = length(metric_entropies) > 0 ? var(metric_entropies) : 0

    return variance < 0.1  # Seuil de stabilité
end


"""
    predict_bayesian_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, observed_frequencies::Dict{String, Any})::Union{String, Nothing}

Prédiction bayésienne utilisant probabilités théoriques INDEX5
"""
function predict_bayesian_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, observed_frequencies::Dict{String, Any})::Union{String, Nothing}
    # 1. Calculer probabilités conditionnelles observées
    conditional_probs = calculate_conditional_probabilities(predictor, sequence_history)

    # 2. Pondérer avec probabilités théoriques (Bayes)
    bayesian_probs = Dict{String, Float64}()
    for index5_value in keys(predictor.THEORETICAL_PROBS)
        # P(Xₙ₊₁|contexte) ∝ P(contexte|Xₙ₊₁) × P(Xₙ₊₁)
        observed_prob = get(conditional_probs, index5_value, 0.0)
        theoretical_prob = predictor.THEORETICAL_PROBS[index5_value]

        bayesian_prob = observed_prob * theoretical_prob
        bayesian_probs[index5_value] = bayesian_prob
    end

    # Normaliser les probabilités
    total_prob = sum(values(bayesian_probs))
    if total_prob > 0
        normalized_probs = Dict{String, Float64}()
        for (k, v) in bayesian_probs
            normalized_probs[k] = v / total_prob
        end

        # Retourner la valeur avec la plus haute probabilité
        best_prediction = argmax(normalized_probs)
        return best_prediction
    end

    return nothing
end


"""
    predict_bayesian_theoretical(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}

Combine observations avec probabilités théoriques INDEX5
"""
function predict_bayesian_theoretical(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}
    # Calculer fréquences observées récentes (20 dernières mains)
    recent_sequence = length(sequence_history) >= 20 ? sequence_history[end-19:end] : sequence_history

    # Compter les fréquences observées
    observed_freq = Dict{String, Int}()
    for value in recent_sequence
        observed_freq[value] = get(observed_freq, value, 0) + 1
    end

    bayesian_probs = Dict{String, Float64}()

    for index5_value in keys(predictor.THEORETICAL_PROBS)
        # Probabilité théorique
        p_theoretical = predictor.THEORETICAL_PROBS[index5_value]

        # Probabilité observée (avec lissage de Laplace)
        observed_count = get(observed_freq, index5_value, 0)
        p_observed = (observed_count + 1) / (length(recent_sequence) + length(predictor.THEORETICAL_PROBS))

        # Fusion Bayésienne (pondération adaptative selon la prédictibilité)
        predictability = get(metrics, "predictability_score", 0.5)

        # Plus c'est prévisible, plus on fait confiance aux observations
        bayesian_prob = (predictability * p_observed +
                        (1 - predictability) * p_theoretical)

        bayesian_probs[index5_value] = bayesian_prob
    end

    if !isempty(bayesian_probs)
        return argmax(bayesian_probs)
    end

    return nothing
end


"""
    predict_compression_patterns(predictor::INDEX5Predictor, sequence_history::Vector{String})::Union{String, Nothing}

Exploite les patterns de compression pour prédiction
"""
function predict_compression_patterns(predictor::INDEX5Predictor, sequence_history::Vector{String})::Union{String, Nothing}
    # Chercher les patterns répétitifs les plus récents
    for pattern_len in 2:min(5, length(sequence_history)-1)
        recent_pattern = sequence_history[end-pattern_len+1:end]

        # Chercher ce pattern dans l'historique
        continuation = find_exact_pattern_continuation(predictor, recent_pattern, sequence_history)
        if continuation !== nothing
            return continuation
        end
    end

    return nothing
end


"""
    predict_context_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Union{String, Nothing}

Analyse contextuelle temporelle pour prédiction INDEX5
Utilise entropie conditionnelle et taux de répétition
"""
function predict_context_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Union{String, Nothing}
    if length(sequence_history) < 5
        return nothing
    end

    # 1. Analyser les 5-10 dernières mains pour patterns courts
    recent_pattern = sequence_history[end-4:end]

    # 2. Si entropie conditionnelle < 3.8 bits → Forte prédictibilité
    # CORRECTION AEP: Nouveau seuil basé sur les vraies valeurs observées (minimum ~3.7 bits)
    if get(current_metrics, "conditional_entropy", 6.2192) < 3.8
        # Chercher pattern exact dans l'historique
        return find_exact_pattern_continuation(predictor, recent_pattern, sequence_history)
    end

    # 3. Si taux répétition > 15% → Tendance répétitive
    if get(current_metrics, "repetition_rate", 0) > 0.15
        return predict_repetition_bias(predictor, sequence_history[end])
    end

    return nothing
end


"""
    predict_deterministic_model(predictor::INDEX5Predictor, sequence_history::Vector{String})::Union{String, Nothing}

Modèle déterministe basé sur les transitions
"""
function predict_deterministic_model(predictor::INDEX5Predictor, sequence_history::Vector{String})::Union{String, Nothing}
    return predict_transition_analysis(predictor, sequence_history, Dict{String, Any}())
end


"""
    predict_deterministic_patterns(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}

Exploite les patterns récurrents détectés
"""
function predict_deterministic_patterns(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}
    pattern_predictions = Dict{String, Float64}()

    for pattern_length in 2:5
        if length(sequence_history) >= pattern_length
            current_pattern = sequence_history[end-pattern_length+1:end]

            # Chercher ce pattern dans l'historique
            continuations = find_pattern_continuations(predictor, current_pattern, sequence_history)

            if !isempty(continuations)
                # Pondérer par fréquence et récence
                for (continuation, freq) in continuations
                    weight = freq * (1.0 / pattern_length)  # Patterns courts = plus fiables
                    pattern_predictions[continuation] = get(pattern_predictions, continuation, 0.0) + weight
                end
            end
        end
    end

    if !isempty(pattern_predictions)
        # Normaliser et retourner le meilleur
        total_weight = sum(values(pattern_predictions))
        normalized = Dict{String, Float64}()
        for (k, v) in pattern_predictions
            normalized[k] = v / total_weight
        end
        return argmax(normalized)
    end

    return nothing
end


"""
    predict_entropy_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, entropy_evolution::Vector{Dict{String, Any}})::Union{String, Nothing}

Prédiction basée sur l'analyse entropique avancée
Utilise entropie métrique, complexité LZ, entropie topologique
"""
function predict_entropy_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, entropy_evolution::Vector{Dict{String, Any}})::Union{String, Nothing}
    if isempty(entropy_evolution)
        return nothing
    end

    current_metrics = !isempty(entropy_evolution) ? entropy_evolution[end] : Dict{String, Any}()

    # 1. Si entropie métrique stable → Système déterministe
    recent_evolution = length(entropy_evolution) >= 10 ? entropy_evolution[end-9:end] : entropy_evolution
    if is_metric_entropy_stable(predictor, recent_evolution)
        # Utiliser modèle déterministe basé sur transitions
        return predict_deterministic_model(predictor, sequence_history)
    end

    # 2. Si complexité LZ faible → Séquence compressible
    if get(current_metrics, "lz_complexity", 100) < 35
        # Exploiter patterns de compression
        return predict_compression_patterns(predictor, sequence_history)
    end

    # 3. Si entropie topologique élevée → Richesse structurelle
    # CORRECTION AEP: Nouveau seuil basé sur les valeurs observées (3.8-4.1 bits)
    if get(current_metrics, "topological_entropy", 0) > 4.05
        # Modèle sophistiqué multi-patterns
        return predict_rich_structure_model(predictor, sequence_history)
    end

    return nothing
end


"""
    predict_frequency_based(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}

Prédiction basée sur les fréquences observées
"""
function predict_frequency_based(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}
    # Analyser les fréquences récentes
    recent_sequence = length(sequence_history) >= 30 ? sequence_history[end-29:end] : sequence_history

    # Compter les fréquences
    freq_counter = Dict{String, Int}()
    for value in recent_sequence
        freq_counter[value] = get(freq_counter, value, 0) + 1
    end

    if !isempty(freq_counter)
        # Retourner la valeur la plus fréquente récemment
        return argmax(freq_counter)
    end

    return nothing
end


"""
    predict_next_index5(predictor::INDEX5Predictor, sequence_history::Vector{String}, all_metrics::Dict{String, Any})::Union{Dict{String, Any}, Nothing}

Prédicteur INDEX5 principal utilisant fusion multi-algorithmes
AVEC CONTRAINTE INDEX1 DÉTERMINISTE APPLIQUÉE AVANT LE VOTE
"""
function predict_next_index5(predictor::INDEX5Predictor, sequence_history::Vector{String}, all_metrics::Dict{String, Any})::Union{Dict{String, Any}, Nothing}
    if isempty(sequence_history) || isempty(all_metrics)
        return nothing
    end

    # ÉTAPE 1: Déterminer INDEX1 obligatoire selon les règles déterministes
    current_index5 = sequence_history[end]
    required_index1 = calculate_required_index1(predictor, current_index5)

    if required_index1 === nothing
        return nothing
    end

    # ÉTAPE 2: Obtenir la liste des INDEX5 valides
    valid_index5_values = get_valid_index5_values(predictor, required_index1)

    # ÉTAPE 3: Évaluation de la prédictibilité actuelle
    conditional_entropy = get(all_metrics, "conditional_entropy", 6.2192)
    current_predictability = max(0.0, (6.2192 - conditional_entropy) / 6.2192)

    # ÉTAPE 4: Sélection de la stratégie optimale
    weight_deterministic, weight_bayesian, weight_frequency = if current_predictability > 0.40  # Très prévisible
        (0.7, 0.2, 0.1)
    elseif current_predictability > 0.30  # Prévisible
        (0.5, 0.3, 0.2)
    else  # Moins prévisible
        (0.3, 0.5, 0.2)
    end

    # ÉTAPE 5: Calcul des prédictions FILTRÉES par chaque méthode
    pred_deterministic_raw = predict_deterministic_patterns(predictor, sequence_history, all_metrics)
    pred_bayesian_raw = predict_bayesian_theoretical(predictor, sequence_history, all_metrics)
    pred_frequency_raw = predict_frequency_based(predictor, sequence_history, all_metrics)

    # FILTRAGE : Ne garder que les prédictions qui respectent INDEX1
    pred_deterministic = filter_prediction_by_constraint(predictor, pred_deterministic_raw, valid_index5_values)
    pred_bayesian = filter_prediction_by_constraint(predictor, pred_bayesian_raw, valid_index5_values)
    pred_frequency = filter_prediction_by_constraint(predictor, pred_frequency_raw, valid_index5_values)

    # ÉTAPE 6: Fusion pondérée des prédictions VALIDES uniquement
    predictions = Tuple{String, String, Float64}[]
    if pred_deterministic !== nothing
        push!(predictions, ("DETERMINISTIC", pred_deterministic, weight_deterministic))
    end
    if pred_bayesian !== nothing
        push!(predictions, ("BAYESIAN", pred_bayesian, weight_bayesian))
    end
    if pred_frequency !== nothing
        push!(predictions, ("FREQUENCY", pred_frequency, weight_frequency))
    end

    # ÉTAPE 7: Si aucune prédiction valide → WAIT
    if isempty(predictions)
        return Dict{String, Any}(
            "predicted_index5" => "WAIT",
            "confidence" => 0.0,
            "predictability_score" => current_predictability,
            "contributing_methods" => String[],
            "constraint_applied" => true,
            "original_prediction" => "WAIT",
            "reason" => "Aucune prédiction valide pour INDEX1=$required_index1"
        )
    end

    # ÉTAPE 8: Vote pondéré sur les prédictions valides
    vote_weights = Dict{String, Float64}()
    for (method, pred, weight) in predictions
        vote_weights[pred] = get(vote_weights, pred, 0.0) + weight
    end

    # Retourner la prédiction avec le plus fort poids
    best_prediction = argmax(vote_weights)
    final_prediction = best_prediction

    # RESTAURATION: Utiliser le poids pondéré cumulé comme dans l'ancien code
    weighted_confidence = vote_weights[best_prediction]

    return Dict{String, Any}(
        "predicted_index5" => final_prediction,
        "confidence" => weighted_confidence,
        "predictability_score" => current_predictability,
        "contributing_methods" => [p[1] for p in predictions if p[2] == final_prediction],
        "constraint_applied" => true,
        "original_prediction" => final_prediction,
        "required_index1" => required_index1
    )
end


"""
    predict_repetition_bias(predictor::INDEX5Predictor, last_value::String)::String

Prédit une répétition de la dernière valeur
"""
function predict_repetition_bias(predictor::INDEX5Predictor, last_value::String)::String
    return last_value
end


"""
    predict_rich_structure_model(predictor::INDEX5Predictor, sequence_history::Vector{String})::Union{String, Nothing}

Modèle sophistiqué pour structures riches
"""
function predict_rich_structure_model(predictor::INDEX5Predictor, sequence_history::Vector{String})::Union{String, Nothing}
    # Combiner plusieurs approches pour structures complexes
    predictions = String[]

    # Analyse de transitions
    trans_pred = predict_transition_analysis(predictor, sequence_history, Dict{String, Any}())
    if trans_pred !== nothing
        push!(predictions, trans_pred)
    end

    # Analyse de patterns
    pattern_pred = predict_compression_patterns(predictor, sequence_history)
    if pattern_pred !== nothing
        push!(predictions, pattern_pred)
    end

    # Retourner la prédiction la plus fréquente
    if !isempty(predictions)
        # Compter les occurrences
        counter = Dict{String, Int}()
        for pred in predictions
            counter[pred] = get(counter, pred, 0) + 1
        end
        # Retourner la plus fréquente
        return argmax(counter)
    end

    return nothing
end


"""
    predict_transition_analysis(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}

Analyse les transitions conditionnelles INDEX5
"""
function predict_transition_analysis(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}
    # Construire matrice de transitions
    transitions = Dict{String, Dict{String, Int}}()

    for i in 1:(length(sequence_history) - 1)
        current = sequence_history[i]
        next_val = sequence_history[i + 1]

        if !haskey(transitions, current)
            transitions[current] = Dict{String, Int}()
        end
        transitions[current][next_val] = get(transitions[current], next_val, 0) + 1
    end

    # Prédire basé sur la dernière valeur
    if !isempty(sequence_history)
        last_value = sequence_history[end]

        if haskey(transitions, last_value)
            # Normaliser les transitions depuis cette valeur
            total_transitions = sum(values(transitions[last_value]))
            transition_probs = Dict{String, Float64}()

            for (next_val, count) in transitions[last_value]
                transition_probs[next_val] = count / total_transitions
            end

            if !isempty(transition_probs)
                return argmax(transition_probs)
            end
        end
    end

    return nothing
end