#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour organiser les fichiers .txt par classe
Lit l'en-tête de chaque fichier pour identifier la classe et déplace le fichier dans le dossier correspondant
"""

import os
import re
import shutil
from pathlib import Path

def extract_class_name(file_path):
    """
    Extrait le nom de la classe depuis l'en-tête du fichier
    Recherche la ligne: "# Type: Méthode de la classe [NOM_CLASSE]"
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # Lire les 10 premières lignes pour trouver l'en-tête
            for i, line in enumerate(f):
                if i >= 10:  # Limiter la recherche aux 10 premières lignes
                    break
                
                # Rechercher le pattern "Type: Méthode de la classe"
                match = re.search(r'# Type: Méthode de la classe\s+(\w+)', line)
                if match:
                    return match.group(1)
                
                # Rechercher aussi le pattern pour les méthodes générales
                if "# Type: Méthode" in line and "classe" not in line:
                    return "General"  # Pour les méthodes qui ne sont pas dans une classe
                    
    except Exception as e:
        print(f"❌ Erreur lors de la lecture de {file_path}: {e}")
        return None
    
    return None

def create_directory_structure(base_path, class_names):
    """
    Crée la structure de dossiers pour chaque classe
    """
    created_dirs = []
    for class_name in class_names:
        class_dir = os.path.join(base_path, class_name)
        if not os.path.exists(class_dir):
            os.makedirs(class_dir)
            created_dirs.append(class_name)
            print(f"📁 Dossier créé: {class_name}")
    
    return created_dirs

def organize_files_by_class(source_directory):
    """
    Organise tous les fichiers .txt par classe
    """
    source_path = Path(source_directory)
    
    if not source_path.exists():
        print(f"❌ Le dossier source n'existe pas: {source_directory}")
        return
    
    print(f"🔍 Analyse du dossier: {source_directory}")
    print("=" * 60)
    
    # Dictionnaire pour stocker les fichiers par classe
    files_by_class = {}
    unclassified_files = []
    
    # Parcourir tous les fichiers .txt
    txt_files = list(source_path.glob("*.txt"))
    print(f"📄 {len(txt_files)} fichiers .txt trouvés")
    
    for txt_file in txt_files:
        print(f"\n🔍 Analyse: {txt_file.name}")
        
        class_name = extract_class_name(txt_file)
        
        if class_name:
            if class_name not in files_by_class:
                files_by_class[class_name] = []
            files_by_class[class_name].append(txt_file)
            print(f"   ✅ Classe identifiée: {class_name}")
        else:
            unclassified_files.append(txt_file)
            print(f"   ⚠️  Classe non identifiée")
    
    # Afficher le résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DE L'ANALYSE:")
    print("=" * 60)
    
    for class_name, files in files_by_class.items():
        print(f"📁 {class_name}: {len(files)} fichiers")
        for file in files[:3]:  # Afficher les 3 premiers
            print(f"   - {file.name}")
        if len(files) > 3:
            print(f"   ... et {len(files) - 3} autres")
    
    if unclassified_files:
        print(f"\n⚠️  Fichiers non classifiés: {len(unclassified_files)}")
        for file in unclassified_files:
            print(f"   - {file.name}")
    
    # Demander confirmation avant de déplacer
    print("\n" + "=" * 60)
    response = input("🤔 Voulez-vous procéder au déplacement des fichiers? (o/n): ").lower().strip()
    
    if response != 'o':
        print("❌ Opération annulée")
        return
    
    # Créer les dossiers
    print("\n📁 Création des dossiers...")
    created_dirs = create_directory_structure(source_directory, files_by_class.keys())
    
    # Déplacer les fichiers
    print("\n📦 Déplacement des fichiers...")
    moved_count = 0
    
    for class_name, files in files_by_class.items():
        class_dir = os.path.join(source_directory, class_name)
        
        for file in files:
            try:
                destination = os.path.join(class_dir, file.name)
                shutil.move(str(file), destination)
                print(f"   ✅ {file.name} → {class_name}/")
                moved_count += 1
            except Exception as e:
                print(f"   ❌ Erreur lors du déplacement de {file.name}: {e}")
    
    # Créer un dossier pour les fichiers non classifiés s'il y en a
    if unclassified_files:
        unclassified_dir = os.path.join(source_directory, "_Unclassified")
        if not os.path.exists(unclassified_dir):
            os.makedirs(unclassified_dir)
            print(f"📁 Dossier créé: _Unclassified")
        
        for file in unclassified_files:
            try:
                destination = os.path.join(unclassified_dir, file.name)
                shutil.move(str(file), destination)
                print(f"   ✅ {file.name} → _Unclassified/")
                moved_count += 1
            except Exception as e:
                print(f"   ❌ Erreur lors du déplacement de {file.name}: {e}")
    
    print("\n" + "=" * 60)
    print("✅ ORGANISATION TERMINÉE!")
    print("=" * 60)
    print(f"📊 {moved_count} fichiers déplacés")
    print(f"📁 {len(created_dirs)} nouveaux dossiers créés")
    
    if unclassified_files:
        print(f"⚠️  {len(unclassified_files)} fichiers dans _Unclassified")

def main():
    """
    Point d'entrée principal
    """
    print("🎯 ORGANISATEUR DE FICHIERS PAR CLASSE")
    print("=" * 60)
    
    # Dossier source
    source_directory = r"C:\Users\<USER>\Desktop\11\entropie_baccarat_analyzer"
    
    # Vérifier que le dossier existe
    if not os.path.exists(source_directory):
        print(f"❌ Le dossier source n'existe pas: {source_directory}")
        return
    
    # Organiser les fichiers
    organize_files_by_class(source_directory)

if __name__ == "__main__":
    main()
