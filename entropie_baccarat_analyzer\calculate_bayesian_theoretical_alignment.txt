# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1540 à 1582
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_bayesian_theoretical_alignment(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule l'alignement entre les observations récentes et les probabilités théoriques
en utilisant une approche bayésienne. Plus le score est élevé, plus les observations
récentes sont alignées avec la théorie.
"""
function calculate_bayesian_theoretical_alignment(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 10
        return 0.0
    end

    # Analyser les 20 dernières observations
    recent_sequence = length(sequence_history) >= 20 ? sequence_history[end-19:end] : sequence_history
    observed_freq = Dict{String, Int}()
    for value in recent_sequence
        observed_freq[value] = get(observed_freq, value, 0) + 1
    end

    # Calculer le score d'alignement bayésien
    alignment_scores = Float64[]

    for index5_value in keys(calculator.THEORETICAL_PROBS)
        p_theoretical = calculator.THEORETICAL_PROBS[index5_value]
        observed_count = get(observed_freq, index5_value, 0)
        p_observed = observed_count / length(recent_sequence)

        # Score d'alignement pour cette valeur (1 - différence relative)
        if p_theoretical > 0
            relative_diff = abs(p_observed - p_theoretical) / p_theoretical
            alignment_score = max(0.0, 1.0 - relative_diff)
            push!(alignment_scores, alignment_score)
        end
    end

    if !isempty(alignment_scores)
        # Score moyen pondéré par les probabilités théoriques
        weighted_alignment = 0.0
        total_weight = 0.0

        for (i, (index5_value, p_theoretical)) in enumerate(calculator.THEORETICAL_PROBS)
            if i <= length(alignment_scores)
                weighted_alignment += alignment_scores[i] * p_theoretical
                total_weight += p_theoretical
            end
        end

        if total_weight > 0
            return round(weighted_alignment / total_weight, digits=4)
        end
    end

    return 0.0
end