# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 277 à 339
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    calculate_block_entropy_evolution(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_block_length::Int=5)::Vector{Dict{String, Any}}

📈 ÉVOLUTION - Calcule l'évolution de l'entropie par blocs (Kolmogorov-Sinai)

Méthode centrale d'analyse qui calcule position par position :
- Entropie de Shannon observée et théorique
- Entropie conditionnelle H(Xₙ|Xₙ₋₁)
- Entropie métrique h_μ(T)
- Taux d'entropie asymptotique

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
Méthode: entropie/cours_entropie/ressources/implementations_python.py (metric_entropy_estimate)

# Arguments
- `sequence::Vector{String}`: Séquence des valeurs INDEX5
- `max_block_length::Int`: Longueur maximale des blocs à analyser

# Returns
- `Vector{Dict{String, Any}}`: Liste des résultats d'entropie pour chaque position
"""
function calculate_block_entropy_evolution(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_block_length::Int=5)::Vector{Dict{String, Any}}
    results = Dict{String, Any}[]

    for n in 1:length(sequence)
        # Sous-séquence de longueur n
        subsequence = sequence[1:n]

        # Calcul de l'entropie de blocs pour différentes longueurs
        block_entropies = _calculate_block_entropies(analyzer, subsequence, max_block_length)

        # Entropie conditionnelle (prédictibilité du prochain symbole)
        conditional_entropy = _calculate_conditional_entropy(analyzer, subsequence)

        # Estimation de l'entropie métrique (taux de création d'information)
        metric_entropy = _estimate_metric_entropy(analyzer, subsequence, max_block_length)

        # Comptage simple pour comparaison (fréquences observées)
        counts = Dict{String, Int}()
        for value in subsequence
            counts[value] = get(counts, value, 0) + 1
        end
        total = length(subsequence)
        empirical_probs = [counts[value] / total for value in keys(counts)]

        # CORRECTION: Entropie simple observée basée sur fréquences empiriques
        # Calcul de l'entropie de Shannon sur les fréquences observées dans la sous-séquence
        simple_entropy_observed = _calculate_shannon_entropy(analyzer, empirical_probs)

        # CORRECTION: Entropie simple avec probabilités théoriques (formule AEP)
        simple_entropy_theoretical = _calculate_sequence_entropy_aep(analyzer, subsequence)

        # Créer les dictionnaires pour les probabilités empiriques
        empirical_probabilities = Dict{String, Float64}()
        for (value, count) in counts
            empirical_probabilities[value] = count / total
        end

        push!(results, Dict{String, Any}(
            "position" => n,
            "sequence_length" => n,
            "unique_values" => length(counts),
            "simple_entropy" => simple_entropy_observed,  # Entropie de Shannon sur fréquences observées
            "simple_entropy_theoretical" => simple_entropy_theoretical,  # Entropie AEP avec probabilités théoriques
            "block_entropies" => block_entropies,  # H(blocs de longueur k)
            "conditional_entropy" => conditional_entropy,  # H(Xₙ|X₁,...,Xₙ₋₁)
            "metric_entropy" => metric_entropy,  # Estimation h_μ(T)
            "entropy_rate" => !isempty(block_entropies) ? block_entropies[end] : 0.0,  # Taux d'entropie
            "observed_values" => collect(keys(counts)),
            "counts" => counts,
            "empirical_probabilities" => empirical_probabilities
        ))
    end

    return results
end