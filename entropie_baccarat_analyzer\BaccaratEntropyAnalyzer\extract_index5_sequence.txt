# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 230 à 271
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    extract_index5_sequence(analyzer::BaccaratEntropyAnalyzer, game_data::Dict{String, Any})::Vector{String}

🔍 EXTRACTION - Extrait la séquence INDEX5 d'une partie

Filtre les mains d'ajustement et extrait uniquement les valeurs INDEX5 valides
pour l'analyse entropique.

# Arguments
- `game_data::Dict{String, Any}`: Données d'une partie

# Returns
- `Vector{String}`: Séquence des valeurs INDEX5 (sans les mains d'ajustement)
"""
function extract_index5_sequence(analyzer::BaccaratEntropyAnalyzer, game_data::Dict{String, Any})::Vector{String}
    sequence = String[]

    # Vérifier différentes structures possibles
    if haskey(game_data, "hands")
        # Structure: {"hands": [...]}
        for hand in game_data["hands"]
            # Exclure les mains d'ajustement (main_number null ou INDEX5 vide)
            if (haskey(hand, "main_number") && hand["main_number"] !== nothing &&
                haskey(hand, "INDEX5") &&
                hand["INDEX5"] !== nothing &&
                !isempty(strip(hand["INDEX5"])))
                push!(sequence, hand["INDEX5"])
            end
        end

    elseif haskey(game_data, "mains_condensees")
        # Structure: {"mains_condensees": [...]}
        for main in game_data["mains_condensees"]
            # Exclure les mains d'ajustement (main_number null ou index5 vide)
            if (haskey(main, "main_number") && main["main_number"] !== nothing &&
                haskey(main, "index5") &&
                main["index5"] !== nothing &&
                !isempty(strip(main["index5"])))
                push!(sequence, main["index5"])
            end
        end
    else
        println("❌ Structure de partie non reconnue. Clés disponibles: $(collect(keys(game_data)))")
        return String[]
    end

    println("🔍 Séquence extraite: $(length(sequence)) mains valides (mains d'ajustement exclues)")

    return sequence
end