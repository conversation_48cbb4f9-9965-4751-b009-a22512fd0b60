# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 415 à 453
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _calculate_sequence_entropy_aep(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64

Calcule l'entropie d'une séquence selon la formule AEP exacte :
H_séquence = -(1/n) × log₂ p(X₁, X₂, ..., Xₙ)
où p(X₁, X₂, ..., Xₙ) = ∏ᵢ₌₁ⁿ p_théo(xᵢ) pour séquences indépendantes

Référence: Elements of Information Theory - ligne 1919
"The AEP states that (1/n) log (1/p(X₁, X₂, ..., Xₙ)) is close to the entropy H"

Cette formule unifiée remplace tous les calculs d'entropie de séquence
pour garantir la cohérence mathématique selon la théorie de l'information.

# Arguments
- `sequence::Vector{String}`: Liste des valeurs INDEX5 dans la séquence

# Returns
- `Float64`: Entropie de la séquence selon AEP en bits
"""
function _calculate_sequence_entropy_aep(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64
    if isempty(sequence)
        return 0.0
    end

    # Calculer -log₂ de la probabilité jointe théorique
    # p(séquence) = ∏ᵢ₌₁ⁿ p_théo(xᵢ)
    # log₂(p(séquence)) = ∑ᵢ₌₁ⁿ log₂(p_théo(xᵢ))
    total_log_prob = 0.0
    for value in sequence
        if haskey(analyzer.theoretical_probs, value)
            p_theo = analyzer.theoretical_probs[value]
            if p_theo > 0
                total_log_prob += log2(p_theo)
            else
                # Si probabilité théorique = 0, utiliser une valeur très faible
                total_log_prob += log2(analyzer.epsilon)
            end
        else
            # Si valeur non trouvée dans les probabilités théoriques, utiliser une probabilité très faible
            total_log_prob += log2(analyzer.epsilon)
        end
    end

    # Retourner l'entropie de la séquence : -(1/n) × ∑log₂(p_théo(xᵢ))
    return -total_log_prob / length(sequence)
end