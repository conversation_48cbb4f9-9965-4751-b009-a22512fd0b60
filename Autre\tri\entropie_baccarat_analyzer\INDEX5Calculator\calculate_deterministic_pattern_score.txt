# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1489 à 1522
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_deterministic_pattern_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule un score de déterminisme basé sur la récurrence des patterns.
Plus le score est élevé, plus la séquence présente des patterns déterministes.
"""
function calculate_deterministic_pattern_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 4
        return 0.0
    end

    pattern_scores = Float64[]

    # Analyser patterns de longueur 2 à 5
    for pattern_length in 2:min(5, length(sequence_history))
        current_pattern = sequence_history[end-pattern_length+1:end]

        # Chercher ce pattern dans l'historique (excluant la fin)
        continuations = find_pattern_continuations(calculator, current_pattern, sequence_history[1:end-pattern_length])

        if !isempty(continuations)
            # Calculer la prévisibilité de ce pattern
            total_continuations = sum(values(continuations))
            max_continuation = maximum(values(continuations))

            # Score = (fréquence max / total) * poids de longueur
            pattern_predictability = max_continuation / total_continuations
            length_weight = pattern_length / 5.0  # Normalisation

            pattern_score = pattern_predictability * length_weight
            push!(pattern_scores, pattern_score)
        end
    end

    if !isempty(pattern_scores)
        # Retourner le score maximum (meilleur pattern déterministe)
        return round(maximum(pattern_scores), digits=4)
    end

    return 0.0
end