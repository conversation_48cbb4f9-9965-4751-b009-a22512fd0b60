# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2698 à 2709
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

    def get_valid_index5_values(self, required_index1):
        """
        Retourne les 9 valeurs INDEX5 possibles avec INDEX1 obligatoire
        """
        if required_index1 is None:
            return []

        valid_values = []
        for index2 in ['A', 'B', 'C']:
            for index3 in ['BANKER', 'PLAYER', 'TIE']:
                valid_values.append(f"{required_index1}_{index2}_{index3}")
        return valid_values