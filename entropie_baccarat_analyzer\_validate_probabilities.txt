# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 136 à 157
# Type: Méthode de la classe BaccaratEntropyAnalyzer

    def _validate_probabilities(self, p: np.ndar<PERSON>) -> np.ndarray:
        """
        ✅ VALIDATION - Valide et normalise un vecteur de probabilités

        Assure que les probabilités sont positives et normalisées (somme = 1).
        Applique une distribution uniforme si toutes les probabilités sont nulles.

        Référence: entropie/cours_entropie/ressources/implementations_python.py
        """
        p = np.array(p, dtype=float)
        
        if np.any(p < 0):
            raise ValueError("Les probabilités doivent être positives")
        
        total = np.sum(p)
        if total > 0:
            p = p / total
        else:
            # Distribution uniforme si toutes les probabilités sont nulles
            p = np.ones_like(p) / len(p)
            
        return p