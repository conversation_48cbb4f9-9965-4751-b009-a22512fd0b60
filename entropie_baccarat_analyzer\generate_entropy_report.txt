# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 771 à 1072
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    generate_entropy_report(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any})::String

Génère un rapport détaillé d'analyse d'entropie selon les méthodes avancées.

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md

# Arguments
- `analysis_result::Dict{String, Any}`: Résultats d'analyse d'une partie

# Returns
- `String`: Rapport formaté en texte avec métriques avancées
"""
function generate_entropy_report(analyzer::BaccaratEntropyAnalyzer, analysis_result::Dict{String, Any})::String
    if !haskey(analysis_result, "entropy_evolution")
        return "❌ Pas de données d'analyse disponibles"
    end

    evolution = analysis_result["entropy_evolution"]
    game_id = analysis_result["game_id"]
    sequence_length = analysis_result["sequence_length"]
    complexity = get(analysis_result, "complexity_metrics", Dict{String, Any}())

    # Statistiques des nouvelles métriques
    metric_entropies = [item["metric_entropy"] for item in evolution]
    conditional_entropies = [item["conditional_entropy"] for item in evolution]
    entropy_rates = [item["entropy_rate"] for item in evolution]
    simple_entropies = [item["simple_entropy"] for item in evolution]  # Ancienne méthode

    # Valeurs finales
    final_metric = get(analysis_result, "final_metric_entropy", 0)
    final_conditional = get(analysis_result, "final_conditional_entropy", 0)
    final_rate = get(analysis_result, "final_entropy_rate", 0)
    final_simple = get(analysis_result, "final_simple_entropy", 0)

    # Positions des maxima
    max_metric_pos = get(analysis_result, "max_metric_entropy_position", 0)
    max_conditional_pos = get(analysis_result, "max_conditional_entropy_position", 0)

    report = """
🎯 RAPPORT D'ANALYSE D'ENTROPIE AVANCÉE - INDEX5
===============================================
Méthodes: Kolmogorov-Sinai, Entropie de Blocs, Entropie Conditionnelle
Référence: entropie/cours_entropie/niveau_expert/

📊 INFORMATIONS GÉNÉRALES
Partie ID: $game_id
Longueur de la séquence: $sequence_length mains
Entropie théorique maximale: $(round(analyzer.theoretical_entropy, digits=4)) bits

📈 MÉTRIQUES D'ENTROPIE AVANCÉES
┌─ Entropie Métrique (Kolmogorov-Sinai) ─┐
│ Finale: $(round(final_metric, digits=4)) bits/symbole │
│ Maximum: $(round(maximum(metric_entropies), digits=4)) bits (position $max_metric_pos) │
│ Moyenne: $(round(mean(metric_entropies), digits=4)) bits │
└────────────────────────────────────────┘

┌─ Entropie Conditionnelle H(Xₙ|X₁...Xₙ₋₁) ─┐
│ Finale: $(round(final_conditional, digits=4)) bits │
│ Maximum: $(round(maximum(conditional_entropies), digits=4)) bits (position $max_conditional_pos) │
│ Moyenne: $(round(mean(conditional_entropies), digits=4)) bits │
│ → Mesure la prédictibilité du prochain symbole │
└─────────────────────────────────────────────┘

┌─ Taux d'Entropie (Entropy Rate) ─┐
│ Final: $(round(final_rate, digits=4)) bits/symbole │
│ → Limite asymptotique de l'information par symbole │
└──────────────────────────────────┘

📊 COMPARAISON DES MÉTHODES D'ENTROPIE
Entropie simple (fréquences observées): $(round(final_simple, digits=4)) bits
Entropie simple (probabilités théoriques): $(round(get(evolution[end], "simple_entropy_theoretical", 0), digits=4)) bits
Entropie métrique (Kolmogorov-Sinai): $(round(final_metric, digits=4)) bits
Différence observée vs métrique: $(round(abs(final_simple - final_metric), digits=4)) bits
Différence théorique vs métrique: $(round(abs(get(evolution[end], "simple_entropy_theoretical", 0) - final_metric), digits=4)) bits

🔬 ANALYSE DE COMPLEXITÉ
"""

    # Ajout des métriques de complexité
    if !isempty(complexity)
        report *= """Complexité Lempel-Ziv: $(get(complexity, "lz_complexity", "N/A"))
Entropie Topologique: $(round(get(complexity, "topological_entropy", 0), digits=4)) bits
Diversité relative: $(round(get(complexity, "sequence_diversity", 0)*100, digits=1))%
Taux de répétition: $(round(get(complexity, "repetition_rate", 0)*100, digits=1))%

🎲 MOTIFS UNIQUES OBSERVÉS
"""
        unique_patterns = get(complexity, "unique_patterns", Dict{String, Any}())
        for (length, count) in unique_patterns
            length_num = split(length, "_")[2]
            report *= "Longueur $length_num: $count motifs uniques\n"
        end
    end

    # Initialiser le calculateur, prédicteur, validateur et analyseur différentiel INDEX5
    # CORRECTION: Passer l'instance analyzer pour accéder à _calculate_sequence_entropy_aep
    calculator = INDEX5Calculator(analyzer)
    predictor = INDEX5Predictor()
    validator = INDEX5PredictionValidator()
    differential_analyzer = INDEX5DifferentialAnalyzer()

    # Calculer les différentiels
    differentials = calculate_differentials(differential_analyzer, evolution)

    report *= """
📋 ÉVOLUTION COMPLÈTE - TOUTES LES $(length(evolution)) MAINS AVEC MÉTRIQUES ET PRÉDICTIONS INDEX5
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | Uniques | Métriques INDEX5 | Prédiction POUR cette main
---------|-------------|----------|----------------|------|-------------|--------------|---------|------------------|---------------------------
"""

    # Pré-calculer toutes les prédictions pour éviter le décalage
    sequence = get(analysis_result, "full_sequence", String[])
    predictions = ["N/A"]  # Première main n'a pas de prédiction

    for i in 1:(length(evolution) - 1)
        item = evolution[i]
        sequence_up_to_i = i <= length(sequence) ? sequence[1:i] : sequence
        current_metrics = Dict{String, Any}(
            "conditional_entropy" => get(item, "conditional_entropy", 0),
            "metric_entropy" => get(item, "metric_entropy", 0),
            "repetition_rate" => get(item, "entropy_rate", 0),
            "predictability_score" => 1 - (get(item, "conditional_entropy", 3.9309) / 3.9309)
        )

        # Calculer la prédiction pour la main suivante (i+1)
        if length(sequence_up_to_i) >= 5  # Minimum requis pour prédiction
            prediction_result = predict_next_index5(predictor, sequence_up_to_i, current_metrics)
            if prediction_result !== nothing && isa(prediction_result, Dict)
                predicted_value = get(prediction_result, "predicted_index5", "N/A")
                confidence = get(prediction_result, "confidence", 0)

                # Gestion spéciale pour WAIT
                if predicted_value == "WAIT"
                    prediction_display = "WAIT"
                else
                    prediction_display = "$predicted_value($(round(confidence, digits=2)))"
                end
            else
                # Essayer prédiction contextuelle simple
                simple_pred = predict_context_level(predictor, sequence_up_to_i, current_metrics)
                prediction_display = simple_pred !== nothing ? simple_pred : "N/A"
            end
        else
            prediction_display = "N/A"
        end

        push!(predictions, prediction_display)

            # Valider la prédiction avec la valeur réelle suivante
            if prediction_display != "N/A":
                next_actual_index5 = sequence[i + 1]
                validator.validate_prediction(prediction_display, next_actual_index5, item['position'] + 1)

        # Générer le rapport avec les prédictions correctement alignées
        for i, item in enumerate(evolution):
            simple_theo = item.get('simple_entropy_theoretical', 0)
            index5_value = sequence[i] if i < len(sequence) else "N/A"

            # Calculer les métriques INDEX5 pour cette position
            sequence_up_to_i = sequence[:i+1] if i < len(sequence) else sequence
            current_metrics = {
                'conditional_entropy': item.get('conditional_entropy', 0),
                'metric_entropy': item.get('metric_entropy', 0),
                'repetition_rate': item.get('entropy_rate', 0),
                'predictability_score': 1 - (item.get('conditional_entropy', 3.9309) / 3.9309)
            }

            # Calculer les nouvelles métriques INDEX5
            if len(sequence_up_to_i) >= 2:
                index5_metrics = calculator.calculate_all_metrics(sequence_up_to_i, current_metrics, evolution[:i+1])

                # Sélectionner les 3 métriques les plus importantes pour l'affichage
                context_pred = index5_metrics.get('context_predictability', 0)
                pattern_str = index5_metrics.get('pattern_strength', 0)
                consensus = index5_metrics.get('multi_algorithm_consensus', 0)

                metrics_display = f"Ctx:{context_pred:.3f} Pat:{pattern_str:.3f} Con:{consensus:.3f}"
            else:
                metrics_display = "Ctx:0.000 Pat:0.000 Con:0.000"

            # Utiliser la prédiction pré-calculée pour cette main
            prediction_display = predictions[i] if i < len(predictions) else "N/A"

            report += f"Main {item['position']:2d}  | {index5_value:11s} | {item['metric_entropy']:6.3f}  | {item['conditional_entropy']:12.3f}  | {item['entropy_rate']:4.3f} | {item['simple_entropy']:9.3f} | {simple_theo:10.3f} | {item['unique_values']:2d}/18 | {metrics_display} | {prediction_display:14s}\n"

        # Ajouter le nouveau tableau avec les différentiels
        report += f"""

📊 TABLEAU AVEC DIFFÉRENTIELS - ANALYSE DES VARIATIONS ENTRE MAINS
Position | INDEX5      | Métrique | Conditionnelle | Taux | DivEntropG  | EntropG      | DiffCond | DiffTaux | DiffDivEntropG | DiffEntropG | Prédiction
---------|-------------|----------|----------------|------|-------------|--------------|----------|----------|----------------|-------------|------------
"""

        # Générer le tableau avec différentiels
        for i, item in enumerate(evolution):
            simple_theo = item.get('simple_entropy_theoretical', 0)
            index5_value = sequence[i] if i < len(sequence) else "N/A"
            prediction_display = predictions[i] if i < len(predictions) else "N/A"

            # Récupérer les différentiels correspondants
            if i < len(differentials):
                diff_data = differentials[i]
                diff_cond = diff_data.get('diff_conditional', 0)
                diff_taux = diff_data.get('diff_entropy_rate', 0)
                diff_div_entrop = diff_data.get('diff_simple_entropy', 0)
                diff_entrop = diff_data.get('diff_simple_entropy_theoretical', 0)
            else:
                diff_cond = diff_taux = diff_div_entrop = diff_entrop = 0.0

            report += f"Main {item['position']:2d}  | {index5_value:11s} | {item['metric_entropy']:6.3f}  | {item['conditional_entropy']:12.3f}  | {item['entropy_rate']:4.3f} | {item['simple_entropy']:9.3f} | {simple_theo:10.3f} | {diff_cond:6.3f}   | {diff_taux:6.3f}   | {diff_div_entrop:12.3f}   | {diff_entrop:9.3f}   | {prediction_display:14s}\n"

        # Ajouter le nouveau tableau prédictif avec différentiels
        predictive_table_generator = INDEX5PredictiveDifferentialTable()
        predictive_table = predictive_table_generator.generate_predictive_table(
            analysis_result['full_sequence'],
            analysis_result['entropy_evolution'],
            self
        )

        # CORRECTION : Injecter directement les données calculées dans le tableau SCORES
        computed_differentials = predictive_table_generator._differential_cache
        predictive_score_table_generator = INDEX5PredictiveScoreTable()
        predictive_score_table = predictive_score_table_generator.generate_predictive_score_table(
            analysis_result['full_sequence'],
            analysis_result['entropy_evolution'],
            self,
            precomputed_differentials=computed_differentials
        )

        report += f"""

🔮 TABLEAU PRÉDICTIF - DIFFÉRENTIELS POUR LES 9 VALEURS INDEX5 POSSIBLES
═══════════════════════════════════════════════════════════════════════

{predictive_table}

{predictive_score_table}
"""

        # Ajouter les statistiques des différentiels
        diff_stats = differential_analyzer.get_differential_statistics(differentials)
        if diff_stats:
            report += f"""

📈 STATISTIQUES DES DIFFÉRENTIELS
═══════════════════════════════

🔢 DIFFÉRENTIELS ENTROPIE CONDITIONNELLE (DiffCond)
• Minimum: {diff_stats.get('diff_conditional', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_conditional', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_conditional', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_conditional', {}).get('std', 0):.4f} bits

🔢 DIFFÉRENTIELS TAUX D'ENTROPIE (DiffTaux)
• Minimum: {diff_stats.get('diff_entropy_rate', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_entropy_rate', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_entropy_rate', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_entropy_rate', {}).get('std', 0):.4f} bits

🔢 DIFFÉRENTIELS DIVERSITÉ ENTROPIQUE (DiffDivEntropG)
• Minimum: {diff_stats.get('diff_simple_entropy', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_simple_entropy', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_simple_entropy', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_simple_entropy', {}).get('std', 0):.4f} bits

🔢 DIFFÉRENTIELS ENTROPIE GÉNÉRALE (DiffEntropG)
• Minimum: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('min', 0):.4f} bits
• Maximum: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('max', 0):.4f} bits
• Moyenne: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('mean', 0):.4f} bits
• Écart-type: {diff_stats.get('diff_simple_entropy_theoretical', {}).get('std', 0):.4f} bits
"""

        # Ajout d'analyses statistiques détaillées
        report += f"""
📊 ANALYSES STATISTIQUES COMPLÈTES
═══════════════════════════════════

🔢 STATISTIQUES D'ENTROPIE MÉTRIQUE
• Minimum: {min(metric_entropies):.4f} bits (main {metric_entropies.index(min(metric_entropies)) + 1})
• Maximum: {max(metric_entropies):.4f} bits (main {metric_entropies.index(max(metric_entropies)) + 1})
• Écart-type: {np.std(metric_entropies):.4f} bits
• Coefficient de variation: {np.std(metric_entropies)/np.mean(metric_entropies)*100:.1f}%

🔢 STATISTIQUES D'ENTROPIE CONDITIONNELLE
• Minimum: {min(conditional_entropies):.4f} bits (main {conditional_entropies.index(min(conditional_entropies)) + 1})
• Maximum: {max(conditional_entropies):.4f} bits (main {conditional_entropies.index(max(conditional_entropies)) + 1})
• Écart-type: {np.std(conditional_entropies):.4f} bits
• Coefficient de variation: {np.std(conditional_entropies)/np.mean(conditional_entropies)*100:.1f}%

🔢 ÉVOLUTION DE LA DIVERSITÉ
• Diversité initiale: {evolution[0]['unique_values']}/18 ({evolution[0]['unique_values']/18*100:.1f}%)
• Diversité finale: {evolution[-1]['unique_values']}/18 ({evolution[-1]['unique_values']/18*100:.1f}%)
• Croissance de diversité: +{evolution[-1]['unique_values'] - evolution[0]['unique_values']} valeurs uniques

🎯 POINTS D'INTÉRÊT IDENTIFIÉS
• Main avec entropie métrique maximale: {max_metric_pos} ({max(metric_entropies):.4f} bits)
• Main avec entropie conditionnelle maximale: {max_conditional_pos} ({max(conditional_entropies):.4f} bits)
• Stabilisation de l'entropie métrique: {"Oui" if np.std(metric_entropies[-10:]) < 0.05 else "Non"} (10 dernières mains)

🔍 INTERPRÉTATION AVANCÉE
• Entropie métrique moyenne ({np.mean(metric_entropies):.3f} bits) = {np.mean(metric_entropies)/self.theoretical_entropy*100:.1f}% du maximum théorique
• Entropie conditionnelle faible → Forte dépendance temporelle, patterns récurrents exploitables
• Taux d'entropie stable → Information moyenne générée par symbole à long terme
• Complexité LZ ({complexity.get('lz_complexity', 'N/A')}) → Séquence {"hautement" if complexity.get('lz_complexity', 60) < 40 else "modérément"} compressible
• Coefficient de variation faible → Comportement {"stable" if np.std(metric_entropies)/np.mean(metric_entropies) < 0.2 else "variable"}

{validator.get_detailed_report()}
"""

        return report