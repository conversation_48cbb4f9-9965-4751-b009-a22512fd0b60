# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1369 à 1398
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_bayesian_divergence_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule la divergence entre les probabilités observées et théoriques INDEX5
en utilisant la divergence de Kullback-Leibler. Plus le score est élevé,
plus la séquence s'écarte des probabilités théoriques.
"""
function calculate_bayesian_divergence_score(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 10
        return 0.0
    end

    # 1. Calculer les fréquences observées
    observed_counts = Dict{String, Int}()
    for value in sequence_history
        observed_counts[value] = get(observed_counts, value, 0) + 1
    end
    total_observations = length(sequence_history)

    # 2. Calculer la divergence KL: D_KL(P_obs || P_theo) = Σ P_obs(x) log(P_obs(x) / P_theo(x))
    kl_divergence = 0.0

    for index5_value in keys(calculator.THEORETICAL_PROBS)
        p_theoretical = calculator.THEORETICAL_PROBS[index5_value]
        p_observed = get(observed_counts, index5_value, 0) / total_observations

        if p_observed > 0  # Éviter log(0)
            kl_divergence += p_observed * log2(p_observed / p_theoretical)
        end
    end

    # 3. Normaliser le score (la divergence KL peut être très élevée)
    # Utiliser une fonction sigmoïde pour normaliser entre 0 et 1
    normalized_score = 2 / (1 + exp(-kl_divergence)) - 1

    return round(max(0.0, normalized_score), digits=4)
end