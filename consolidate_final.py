#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour consolider tous les fichiers .txt par classe selon leur en-tête
Lit la ligne 4 de chaque fichier pour identifier sa classe et consolide tout le code
"""

import os
import re
from pathlib import Path
from collections import defaultdict

def extract_class_from_header(file_path):
    """
    Extrait le nom de la classe depuis l'en-tête du fichier
    Recherche dans la ligne 4: "# Type: Méthode de la classe [NomClasse]" ou "# Type: Méthode"
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
            if len(lines) >= 4:
                # Ligne 4 contient le type et la classe
                type_line = lines[3].strip()
                
                # Rechercher le pattern "classe [NomClasse]"
                class_match = re.search(r'classe\s+([A-Za-z0-9_]+)', type_line)
                if class_match:
                    return class_match.group(1)
                
                # Si pas de "classe", c'est probablement "General" (main.txt)
                if "Méthode" in type_line and "classe" not in type_line:
                    return "General"
                    
    except Exception as e:
        print(f"   ⚠️  Erreur lors de la lecture de l'en-tête de {file_path}: {e}")
    
    return None

def consolidate_files_by_class(base_directory):
    """
    Consolide tous les fichiers .txt par classe selon leur en-tête
    """
    base_path = Path(base_directory)
    
    if not base_path.exists():
        print(f"❌ Le dossier de base n'existe pas: {base_directory}")
        return
    
    print(f"🔍 CONSOLIDATION PAR CLASSE")
    print("=" * 80)
    print(f"📁 Analyse du dossier: {base_directory}")
    
    # Dictionnaire pour regrouper les fichiers par classe
    files_by_class = defaultdict(list)
    
    # Parcourir tous les fichiers .txt directement dans le dossier principal
    txt_files = [f for f in base_path.iterdir() if f.is_file() and f.suffix == '.txt']
    
    print(f"📄 {len(txt_files)} fichiers .txt trouvés dans le dossier principal")
    print("\n🔍 Analyse des en-têtes...")
    
    # Analyser chaque fichier pour extraire la classe
    for txt_file in txt_files:
        print(f"   📝 Analyse: {txt_file.name}")
        
        class_name = extract_class_from_header(txt_file)
        
        if class_name:
            files_by_class[class_name].append(txt_file)
            print(f"      ✅ Classe identifiée: {class_name}")
        else:
            print(f"      ❌ Impossible d'identifier la classe")
    
    # Afficher le résumé
    print(f"\n📊 RÉSUMÉ DE L'ANALYSE:")
    print("=" * 80)
    
    for class_name in sorted(files_by_class.keys()):
        file_count = len(files_by_class[class_name])
        print(f"📁 {class_name}: {file_count} fichiers")
        for file_path in sorted(files_by_class[class_name])[:3]:  # Afficher les 3 premiers
            print(f"   - {file_path.name}")
        if file_count > 3:
            print(f"   ... et {file_count - 3} autres")
    
    print(f"\n📊 TOTAL: {len(files_by_class)} classes identifiées")
    
    # Demander confirmation
    print(f"\n🤔 Voulez-vous procéder à la consolidation? (o/n): ", end="")
    response = input().lower().strip()
    
    if response != 'o':
        print("❌ Consolidation annulée")
        return
    
    print(f"\n📦 Début de la consolidation...")
    print("=" * 80)
    
    # Consolider chaque classe
    total_classes = 0
    total_files_processed = 0
    
    for class_name in sorted(files_by_class.keys()):
        class_files = files_by_class[class_name]
        print(f"\n🔄 Consolidation de la classe: {class_name}")
        print(f"   📄 {len(class_files)} fichiers à traiter")
        
        # Créer le fichier consolidé
        consolidated_filename = f"{class_name}.txt"
        consolidated_path = base_path / consolidated_filename
        
        try:
            with open(consolidated_path, 'w', encoding='utf-8') as consolidated_file:
                # En-tête du fichier consolidé
                consolidated_file.write(f"# " + "=" * 60 + "\n")
                consolidated_file.write(f"# CLASSE: {class_name}\n")
                consolidated_file.write(f"# Fichier consolidé généré automatiquement\n")
                consolidated_file.write(f"# Nombre de fichiers sources: {len(class_files)}\n")
                consolidated_file.write(f"# " + "=" * 60 + "\n\n")
                
                # Traiter chaque fichier de la classe
                for i, file_path in enumerate(sorted(class_files), 1):
                    print(f"   📝 [{i:2d}/{len(class_files)}] {file_path.name}")
                    
                    # Ajouter un séparateur pour chaque fichier
                    consolidated_file.write(f"# " + "-" * 50 + "\n")
                    consolidated_file.write(f"# FICHIER SOURCE: {file_path.name}\n")
                    consolidated_file.write(f"# " + "-" * 50 + "\n\n")
                    
                    try:
                        # Lire et écrire le contenu du fichier
                        with open(file_path, 'r', encoding='utf-8') as source_file:
                            content = source_file.read()
                            
                            if content.strip():
                                consolidated_file.write(content)
                                if not content.endswith('\n'):
                                    consolidated_file.write('\n')
                            else:
                                consolidated_file.write("# [FICHIER VIDE]\n")
                        
                        # Ajouter un espacement entre les fichiers
                        consolidated_file.write("\n\n")
                        
                    except Exception as e:
                        print(f"   ❌ Erreur lors de la lecture de {file_path.name}: {e}")
                        consolidated_file.write(f"# [ERREUR LORS DE LA LECTURE: {e}]\n\n")
                
                # Pied de page
                consolidated_file.write(f"# " + "=" * 50 + "\n")
                consolidated_file.write(f"# FIN DE LA CLASSE {class_name}\n")
                consolidated_file.write(f"# TOTAL: {len(class_files)} fichiers sources\n")
                consolidated_file.write(f"# " + "=" * 50 + "\n")
            
            print(f"   ✅ Fichier consolidé créé: {consolidated_filename}")
            total_classes += 1
            total_files_processed += len(class_files)
            
        except Exception as e:
            print(f"   ❌ Erreur lors de la création du fichier consolidé: {e}")
    
    # Résumé final
    print(f"\n" + "=" * 80)
    print("✅ CONSOLIDATION TERMINÉE!")
    print("=" * 80)
    print(f"📊 {total_classes} classes consolidées")
    print(f"📄 {total_files_processed} fichiers sources traités")
    
    # Lister les fichiers consolidés créés
    consolidated_files = []
    for class_name in files_by_class.keys():
        consolidated_file = base_path / f"{class_name}.txt"
        if consolidated_file.exists():
            consolidated_files.append(consolidated_file)
    
    if consolidated_files:
        print(f"\n📋 Fichiers consolidés créés:")
        for consolidated_file in sorted(consolidated_files):
            file_size = consolidated_file.stat().st_size
            print(f"   - {consolidated_file.name} ({file_size:,} octets)")

def main():
    """
    Point d'entrée principal
    """
    print("🎯 CONSOLIDATEUR DE FICHIERS PAR CLASSE")
    print("=" * 80)
    
    # Dossier de base
    base_directory = r"C:\Users\<USER>\Desktop\11\entropie_baccarat_analyzer"
    
    # Vérifier que le dossier existe
    if not os.path.exists(base_directory):
        print(f"❌ Le dossier de base n'existe pas: {base_directory}")
        return
    
    # Consolider les fichiers par classe
    consolidate_files_by_class(base_directory)

if __name__ == "__main__":
    main()
