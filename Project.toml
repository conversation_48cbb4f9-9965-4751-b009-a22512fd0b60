name = "BaccaratEntropyAnalyzer"
uuid = "12345678-1234-1234-1234-123456789abc"
version = "1.0.0"
authors = ["Baccarat Entropy Analysis Team"]

[deps]
Statistics = "10745b16-79ce-11e8-11f9-7d13ad32a3b2"
Printf = "de0858da-6303-5e67-8744-51eddeeeb8d7"

[extras]
DataFrames = "a93c6f00-e57d-5684-b7b6-d8193f3e46c0"
CSV = "336ed68f-0bac-5ca0-87d4-7b16caf5d00b"
Plots = "91a5bcdd-55d7-5caf-9e0b-520d859cae80"

[targets]
test = ["DataFrames", "CSV", "Plots"]
