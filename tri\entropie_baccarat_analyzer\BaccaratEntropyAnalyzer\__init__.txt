# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 86 à 121
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    BaccaratEntropyAnalyzer(base::Float64=2.0, epsilon::Float64=1e-12)

🔧 INITIALISATION - Configuration du moteur d'analyse entropique

Initialise l'analyseur d'entropie pour le baccarat avec les probabilités
théoriques exactes INDEX5 et calcule l'entropie théorique maximale.

# Arguments
- `base::Float64`: Base du logarithme (2 pour bits, e pour nats)
- `epsilon::Float64`: Valeur minimale pour éviter log(0)
"""
mutable struct BaccaratEntropyAnalyzer
    base::Float64
    epsilon::Float64
    theoretical_probs::Dict{String, Float64}
    theoretical_entropy::Float64

    function BaccaratEntropyAnalyzer(base::Float64=2.0, epsilon::Float64=1e-12)
        # Probabilités théoriques INDEX5 EXACTES (en pourcentage)
        # CORRECTION EXPERTE: Utilisation des vraies probabilités calculées sur données réelles
        theoretical_probs = Dict{String, Float64}(
            "0_A_BANKER" => 8.5136, "1_A_BANKER" => 8.6389,
            "0_B_BANKER" => 6.4676, "1_B_BANKER" => 6.5479,  # CORRIGÉ: était 7.6907
            "0_C_BANKER" => 7.7903, "1_C_BANKER" => 7.8929,
            "0_A_PLAYER" => 8.5240, "1_A_PLAYER" => 8.6361,
            "0_B_PLAYER" => 7.6907, "1_B_PLAYER" => 7.7888,
            "0_C_PLAYER" => 5.9617, "1_C_PLAYER" => 6.0352,
            "0_A_TIE" => 1.7719, "1_A_TIE" => 1.7978,
            "0_B_TIE" => 1.6281, "1_B_TIE" => 1.6482,
            "0_C_TIE" => 1.3241, "1_C_TIE" => 1.3423
        )

        # Normalisation des probabilités
        total = sum(values(theoretical_probs))
        theoretical_probs = Dict(k => v/total for (k, v) in theoretical_probs)

        # Calcul de l'entropie théorique maximale
        theoretical_entropy = _calculate_shannon_entropy(collect(values(theoretical_probs)), base, epsilon)

        println("🎯 Entropie théorique INDEX5: $(round(theoretical_entropy, digits=4)) bits")

        new(base, epsilon, theoretical_probs, theoretical_entropy)
    end
end

"""
    _calculate_shannon_entropy(probabilities::Vector{Float64}, base::Float64, epsilon::Float64)

Calcule l'entropie de Shannon pour un vecteur de probabilités.
"""
function _calculate_shannon_entropy(probabilities::Vector{Float64}, base::Float64=2.0, epsilon::Float64=1e-12)
    entropy = 0.0
    for p in probabilities
        if p > epsilon
            entropy -= p * log(base, p)
        end
    end
    return entropy
end