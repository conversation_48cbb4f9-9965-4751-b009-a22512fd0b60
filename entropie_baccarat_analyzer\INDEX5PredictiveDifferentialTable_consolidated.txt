# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =
# FICHIER CONSOLIDÉ POUR LA CLASSE: INDEX5PredictiveDifferentialTable
# Généré automatiquement - 7 fichiers sources
# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =

# ------------------------------------------------------------
# FICHIER SOURCE: __init___6.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2662 à 2673
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

"""
    INDEX5PredictiveDifferentialTable()

Initialisation de la classe tableau prédictif
"""
mutable struct INDEX5PredictiveDifferentialTable
    all_index5_values::Vector{String}
    _differential_cache::Dict{Any, Any}

    function INDEX5PredictiveDifferentialTable()
        all_index5_values = [
            "0_A_BANKER", "0_B_BANKER", "0_C_BANKER",
            "0_A_PLAYER", "0_B_PLAYER", "0_C_PLAYER",
            "0_A_TIE", "0_B_TIE", "0_C_TIE",
            "1_A_BANKER", "1_B_BANKER", "1_C_BANKER",
            "1_A_PLAYER", "1_B_PLAYER", "1_C_PLAYER",
            "1_A_TIE", "1_B_TIE", "1_C_TIE"
        ]
        # CORRECTION : Cache pour éviter les recalculs entre tableaux différentiel et scores
        _differential_cache = Dict{Any, Any}()

        new(all_index5_values, _differential_cache)
    end
end


# ------------------------------------------------------------
# FICHIER SOURCE: calculate_predictive_differentials.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2758 à 2861
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

"""
    calculate_predictive_differentials(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, position::Int, analyzer::BaccaratEntropyAnalyzer)::Dict{String, Dict{String, Float64}}

Calcule les différentiels prédictifs pour les 9 valeurs INDEX5 possibles à la main n+1

ALGORITHME CORRIGÉ :
1. À la main n : Récupérer les métriques actuelles (Conditionnelle, Taux, DivEntropG, EntropG)
2. Pour chacune des 9 valeurs INDEX5 possibles à n+1 (selon règles INDEX1) :
   - Simuler l'ajout de cette valeur à la séquence
   - Calculer les nouvelles métriques pour la main n+1 simulée
   - Calculer |métrique(n+1) - métrique(n)| pour chaque métrique

CORRECTION : Utilise un cache pour éviter les recalculs entre tableaux différentiel et scores
"""
function calculate_predictive_differentials(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, position::Int, analyzer::BaccaratEntropyAnalyzer)::Dict{String, Dict{String, Float64}}
    if position > length(sequence) || position > length(evolution)
        return Dict{String, Dict{String, Float64}}()
    end

    # CORRECTION : Vérifier le cache d'abord
    cache_key = (Tuple(sequence), position, objectid(analyzer))
    if haskey(table._differential_cache, cache_key)
        return table._differential_cache[cache_key]
    end

    # CORRECTION : Pour prédire la main n+1, il faut utiliser la main n-1 comme référence pour les règles INDEX1
    if position == 1
        return Dict{String, Dict{String, Float64}}()  # Pas de prédiction possible pour la première main (pas de main précédente)
    end

    # ÉTAPE 1: Obtenir l'INDEX5 de référence (main précédente) et les métriques actuelles
    reference_index5 = sequence[position - 1]  # ✅ CORRECTION : Main précédente pour les règles INDEX1
    current_metrics = evolution[position]       # Métriques de la main actuelle

    # Métriques actuelles à la main n
    current_conditional = get(current_metrics, "conditional_entropy", 0.0)

    # CORRECTION HOMOGÈNE: Calculer le taux actuel sur bloc local [n-3,n-2,n-1,n]
    if length(sequence) >= 4
        local_block_current = sequence[position-3:position]  # [n-3,n-2,n-1,n]
        current_rate = _calculate_sequence_entropy_aep(analyzer, local_block_current)
    else
        current_rate = get(current_metrics, "entropy_rate", 0.0)
    end

    current_simple = get(current_metrics, "simple_entropy", 0.0)
    current_theoretical = get(current_metrics, "simple_entropy_theoretical", 0.0)

    # ÉTAPE 2: Calculer INDEX1 requis pour la main n+1 selon les règles déterministes
    # CORRECTION : Utiliser la main de référence (n-1) pour appliquer les règles INDEX1
    required_index1 = calculate_required_index1(table, reference_index5)
    if required_index1 === nothing
        return Dict{String, Dict{String, Float64}}()
    end

    # ÉTAPE 3: Obtenir les 9 valeurs INDEX5 possibles pour la main n+1
    valid_index5_values = get_valid_index5_values(table, required_index1)

    # ÉTAPE 4: Calculer les différentiels pour chaque valeur INDEX5 possible à n+1
    predictive_differentials = Dict{String, Dict{String, Float64}}()

    for possible_index5 in valid_index5_values
        try
            # ÉTAPE 4.1: Créer une séquence simulée avec la valeur possible ajoutée à n+1
            simulated_sequence = vcat(sequence[1:position], [possible_index5])

            # ÉTAPE 4.2: Calculer les métriques pour la main n+1 simulée

            # Entropie conditionnelle pour la séquence simulée complète
            simulated_conditional = _calculate_conditional_entropy(analyzer, simulated_sequence)

            # Entropie simple (Shannon) pour la séquence simulée complète
            # Calculer les fréquences de la séquence simulée
            counts = Dict{String, Int}()
            for value in simulated_sequence
                counts[value] = get(counts, value, 0) + 1
            end
            total = length(simulated_sequence)
            probabilities = [counts[value] / total for value in keys(counts)]
            simulated_simple = _calculate_shannon_entropy(analyzer, probabilities)

            # Entropie théorique (AEP) pour la séquence simulée complète
            simulated_theoretical = _calculate_sequence_entropy_aep(analyzer, simulated_sequence)

            # CORRECTION HOMOGÈNE: Taux d'entropie sur bloc local de longueur 4
            # Bloc actuel: [n-3,n-2,n-1,n] vs Bloc simulé: [n-2,n-1,n,n+1simulé]
            if length(simulated_sequence) >= 4
                # Calculer l'entropie AEP du bloc local de 4 éléments [n-2,n-1,n,n+1simulé]
                local_block_simulated = simulated_sequence[end-3:end]  # 4 derniers éléments
                simulated_rate = _calculate_sequence_entropy_aep(analyzer, local_block_simulated)
            else
                simulated_rate = simulated_simple
            end

            # ÉTAPE 4.3: Calculer les différentiels absolus |métrique(n+1) - métrique(n)|
            diff_cond = abs(simulated_conditional - current_conditional)
            diff_taux = abs(simulated_rate - current_rate)
            diff_div_entrop = abs(simulated_simple - current_simple)
            diff_entrop = abs(simulated_theoretical - current_theoretical)

        catch e
            # En cas d'erreur, utiliser des valeurs par défaut
            diff_cond = diff_taux = diff_div_entrop = diff_entrop = 0.0
        end

        predictive_differentials[possible_index5] = Dict{String, Float64}(
            "DiffCond" => diff_cond,
            "DiffTaux" => diff_taux,
            "DiffDivEntropG" => diff_div_entrop,
            "DiffEntropG" => diff_entrop
        )
    end

    # CORRECTION : Sauvegarder dans le cache avant de retourner
    table._differential_cache[cache_key] = predictive_differentials
    return predictive_differentials
end


# ------------------------------------------------------------
# FICHIER SOURCE: calculate_required_index1_1.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2675 à 2696
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

"""
    calculate_required_index1(table::INDEX5PredictiveDifferentialTable, current_index5::String)::Union{Int, Nothing}

Calcule INDEX1 obligatoire selon les règles déterministes

Règles:
- Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""
function calculate_required_index1(table::INDEX5PredictiveDifferentialTable, current_index5::String)::Union{Int, Nothing}
    if isempty(current_index5)
        return nothing
    end

    try
        parts = split(current_index5, '_')
        current_index1 = parse(Int, parts[1])
        current_index2 = parts[2]

        if current_index2 == "C"
            return 1 - current_index1  # Inversion
        else  # A ou B
            return current_index1      # Conservation
        end
    catch
        return nothing
    end
end


# ------------------------------------------------------------
# FICHIER SOURCE: calculate_simulated_metrics.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2711 à 2756
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

"""
    calculate_simulated_metrics(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, position::Int, possible_index5::String, analyzer::BaccaratEntropyAnalyzer)::Dict{String, Float64}

Calcule les métriques pour une séquence simulée avec possible_index5 ajouté
"""
function calculate_simulated_metrics(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, position::Int, possible_index5::String, analyzer::BaccaratEntropyAnalyzer)::Dict{String, Float64}
    if position > length(sequence)
        return Dict{String, Float64}()
    end

    # Créer séquence simulée avec la valeur possible ajoutée
    simulated_sequence = vcat(sequence[1:position], [possible_index5])

    # Calculer les métriques de base pour la nouvelle position
    metrics = Dict{String, Float64}()

    try
        # Entropie conditionnelle
        if length(simulated_sequence) >= 2
            metrics["conditional_entropy"] = _calculate_conditional_entropy(analyzer, simulated_sequence)
        else
            metrics["conditional_entropy"] = 0.0
        end

        # Entropie simple (Shannon)
        # Calculer les fréquences de la séquence simulée
        counts = Dict{String, Int}()
        for value in simulated_sequence
            counts[value] = get(counts, value, 0) + 1
        end
        total = length(simulated_sequence)
        probabilities = [counts[value] / total for value in keys(counts)]
        metrics["simple_entropy"] = _calculate_shannon_entropy(analyzer, probabilities)

        # Entropie théorique (AEP)
        metrics["simple_entropy_theoretical"] = _calculate_sequence_entropy_aep(analyzer, simulated_sequence)

        # Taux d'entropie (approximation)
        if length(simulated_sequence) >= 3
            block_entropies = _calculate_block_entropies(analyzer, simulated_sequence, min(5, length(simulated_sequence)))
            if !isempty(block_entropies)
                metrics["entropy_rate"] = block_entropies[end]
            else
                metrics["entropy_rate"] = metrics["simple_entropy"]
            end
        else
            metrics["entropy_rate"] = metrics["simple_entropy"]
        end

    catch e
        # En cas d'erreur, retourner des valeurs par défaut
        metrics = Dict{String, Float64}(
            "conditional_entropy" => 0.0,
            "simple_entropy" => 0.0,
            "simple_entropy_theoretical" => 0.0,
            "entropy_rate" => 0.0
        )
    end

    return metrics
end


# ------------------------------------------------------------
# FICHIER SOURCE: generate_predictive_table.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2966 à 2996
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

"""
    generate_predictive_table(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer)::String

Génère le tableau prédictif complet divisé en deux parties
"""
function generate_predictive_table(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer)::String
    if isempty(sequence) || isempty(evolution)
        return "❌ Données insuffisantes pour générer le tableau prédictif"
    end

    # Générer la première partie (Mains 1-30)
    table_part1 = generate_predictive_table_part(table, sequence, evolution, analyzer, 1, 30, 1)

    # Générer la deuxième partie (Mains 31-60)
    table_part2 = generate_predictive_table_part(table, sequence, evolution, analyzer, 31, 60, 2)

    # Combiner les deux parties avec la légende
    complete_table = table_part1 * "\n\n" * table_part2 * """

📋 LÉGENDE DU TABLEAU PRÉDICTIF :
• DiffC = DiffCond (Différentiel Entropie Conditionnelle)
• DiffT = DiffTaux (Différentiel Taux d'Entropie)
• DivEG = DiffDivEntropG (Différentiel Diversité Entropique)
• EntG = DiffEntropG (Différentiel Entropie Générale)
• Seules les 9 valeurs INDEX5 respectant les règles INDEX1 sont calculées
• N/A = Valeur non calculable (ne respecte pas les règles INDEX1)
• --- = Main non disponible dans cette partie

🔄 RÈGLES INDEX1 DÉTERMINISTES :
• Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
• Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""

    return complete_table
end


# ------------------------------------------------------------
# FICHIER SOURCE: generate_predictive_table_part.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2863 à 2964
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

"""
    generate_predictive_table_part(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, start_main::Int, end_main::Int, part_number::Int)::String

Génère une partie du tableau prédictif avec différentiels et séparateurs verticaux
"""
function generate_predictive_table_part(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, start_main::Int, end_main::Int, part_number::Int)::String
    if isempty(sequence) || isempty(evolution)
        return "❌ Données insuffisantes pour générer le tableau prédictif"
    end

    # Pour le premier tableau, commencer à la main 6 au lieu de la main 1
    if part_number == 1
        actual_start = max(start_main, 6)  # Commencer à la main 6
    else
        actual_start = start_main
    end

    # Ajuster les limites selon la longueur de la séquence
    actual_end = min(end_main, length(sequence))
    if actual_start > length(sequence)
        return "❌ Main $actual_start dépasse la longueur de la séquence ($(length(sequence)) mains)"
    end

    # En-tête du tableau avec séparateurs verticaux
    header_line1 = rpad("INDEX5 n+1", 15)
    header_line2 = " " ^ 15
    header_separator = " " ^ 15  # Séparateur horizontal sous les en-têtes Main
    separator_line = "=" ^ 15

    # Générer l'en-tête pour la plage de mains spécifiée avec séparateurs
    for i in actual_start:actual_end
        header_line1 *= "|" * rpad("Main $i", 24)
        # Construction exacte du format souhaité : |DiffC|DiffT|DivEG|EntG  |
        header_line2 *= rpad("|DiffC|DiffT|DivEG|EntG  ", 25)
        # Séparateur horizontal sous chaque "Main X" mais pas sous "INDEX5 n+1"
        header_separator *= "|" * "-" ^ 24
        separator_line *= "|" * "=" ^ 24
    end

    # Ajouter le séparateur final
    header_line1 *= "|"
    header_line2 *= "|"
    header_separator *= "|"
    separator_line *= "|"

    table = "📊 PARTIE $part_number - MAINS $actual_start À $actual_end\n"
    table *= separator_line * "\n"
    table *= header_line1 * "\n"
    table *= header_separator * "\n"  # Séparateur horizontal entre Main et DiffC DiffT DivEG EntG
    table *= header_line2 * "\n"
    table *= separator_line * "\n"

    # Générer les lignes pour chaque valeur INDEX5 possible avec séparateurs
    for (i, index5_value) in enumerate(table.all_index5_values)
        line = rpad(index5_value, 15)

        for position in (actual_start - 1):(actual_end - 1)  # -1 car les indices commencent à 1 en Julia
            line *= "|"  # Séparateur vertical avant chaque main

            if position + 1 <= length(sequence)  # +1 car position est 0-indexé
                # Calculer les différentiels prédictifs
                predictive_diffs = calculate_predictive_differentials(
                    table, sequence, evolution, position + 1, analyzer  # +1 pour convertir en 1-indexé
                )

                if haskey(predictive_diffs, index5_value)
                    diffs = predictive_diffs[index5_value]
                    # Ajouter des séparateurs verticaux entre les valeurs
                    cell_content = @sprintf("%.3f|%.3f|%.3f|%.3f",
                        diffs["DiffCond"], diffs["DiffTaux"],
                        diffs["DiffDivEntropG"], diffs["DiffEntropG"])
                else
                    cell_content = "N/A  |N/A  |N/A  |N/A   "
                end
            else
                cell_content = "---  |---  |---  |---   "
            end

            # Centrer le contenu dans 24 caractères
            line *= lpad(rpad(cell_content, 12), 24)
        end

        line *= "|"  # Séparateur final
        table *= line * "\n"

        # Ajouter un séparateur horizontal entre 0_C_TIE et 1_A_BANKER
        if index5_value == "0_C_TIE"
            table *= separator_line * "\n"
        end
    end

    # Ajouter une ligne de séparation finale
    table *= separator_line * "\n"

    # NOUVELLE LIGNE : Valeur INDEX5 réellement observée pour chaque main
    observed_line = "OBSERVÉ        "
    for position in (actual_start - 1):(actual_end - 1)  # -1 car les indices commencent à 1 en Julia
        observed_line *= "|"  # Séparateur vertical avant chaque main

        if position + 1 <= length(sequence)  # +1 car position est 0-indexé
            # Récupérer la valeur INDEX5 réellement observée à cette position
            observed_index5 = sequence[position + 1]  # +1 pour convertir en 1-indexé
            # Aligner la valeur observée à gauche dans la cellule
            cell_content = observed_index5
        else
            cell_content = "---"
        end

        observed_line *= rpad(cell_content, 24)
    end

    observed_line *= "|\n"
    table *= observed_line

    # Ligne de séparation finale après la ligne observée
    table *= separator_line * "\n"

    return table
end


# ------------------------------------------------------------
# FICHIER SOURCE: get_valid_index5_values_1.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2698 à 2709
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

"""
    get_valid_index5_values(table::INDEX5PredictiveDifferentialTable, required_index1::Union{Int, Nothing})::Vector{String}

Retourne les 9 valeurs INDEX5 possibles avec INDEX1 obligatoire
"""
function get_valid_index5_values(table::INDEX5PredictiveDifferentialTable, required_index1::Union{Int, Nothing})::Vector{String}
    if required_index1 === nothing
        return String[]
    end

    valid_values = String[]
    for index2 in ["A", "B", "C"]
        for index3 in ["BANKER", "PLAYER", "TIE"]
            push!(valid_values, "$(required_index1)_$(index2)_$(index3)")
        end
    end
    return valid_values
end


# ============================================================
# FIN DU FICHIER CONSOLIDÉ POUR INDEX5PredictiveDifferentialTable
# TOTAL: 7 fichiers sources traités
# ============================================================
