# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 600 à 629
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _calculate_sequence_complexity(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Dict{String, Any}

Calcule diverses métriques de complexité de la séquence.

Référence: entropie/cours_entropie/niveau_expert/02_entropie_topologique.md
"""
function _calculate_sequence_complexity(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Dict{String, Any}
    n = length(sequence)

    # Nombre de motifs uniques de différentes longueurs
    unique_patterns = Dict{String, Int}()
    for length in 1:min(5, n)
        patterns = Set{Tuple}()
        for i in 1:(n - length + 1)
            pattern = Tuple(sequence[i:i+length-1])
            push!(patterns, pattern)
        end
        unique_patterns["length_$length"] = length(patterns)
    end

    # Complexité de Lempel-Ziv (approximation)
    lz_complexity = _approximate_lz_complexity(analyzer, sequence)

    # Entropie topologique approximée
    topological_entropy = _approximate_topological_entropy(analyzer, sequence)

    return Dict{String, Any}(
        "unique_patterns" => unique_patterns,
        "lz_complexity" => lz_complexity,
        "topological_entropy" => topological_entropy,
        "sequence_diversity" => length(Set(sequence)) / length(analyzer.theoretical_probs),  # Diversité relative
        "repetition_rate" => _calculate_repetition_rate(analyzer, sequence)
    )
end