# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1770 à 1791
# Type: Méthode de la classe INDEX5Predictor

"""
    predict_context_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Union{String, Nothing}

Analyse contextuelle temporelle pour prédiction INDEX5
Utilise entropie conditionnelle et taux de répétition
"""
function predict_context_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Union{String, Nothing}
    if length(sequence_history) < 5
        return nothing
    end

    # 1. Analyser les 5-10 dernières mains pour patterns courts
    recent_pattern = sequence_history[end-4:end]

    # 2. Si entropie conditionnelle < 3.8 bits → Forte prédictibilité
    # CORRECTION AEP: Nouveau seuil basé sur les vraies valeurs observées (minimum ~3.7 bits)
    if get(current_metrics, "conditional_entropy", 6.2192) < 3.8
        # Chercher pattern exact dans l'historique
        return find_exact_pattern_continuation(predictor, recent_pattern, sequence_history)
    end

    # 3. Si taux répétition > 15% → Tendance répétitive
    if get(current_metrics, "repetition_rate", 0) > 0.15
        return predict_repetition_bias(predictor, sequence_history[end])
    end

    return nothing
end