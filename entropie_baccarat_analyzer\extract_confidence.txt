# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3067 à 3080
# Type: Méthode de la classe INDEX5PredictionValidator

    def extract_confidence(self, predicted_index5):
        """
        Extrait le score de confiance d'une prédiction
        Format: INDEX5(0.XX) → retourne 0.XX comme float
        """
        if not predicted_index5 or '(' not in predicted_index5:
            return 0.0

        try:
            # Extraire la partie entre parenthèses
            confidence_part = predicted_index5.split('(')[1].split(')')[0]
            return float(confidence_part)
        except (IndexError, ValueError):
            return 0.0