#!/usr/bin/env julia

"""
Script de test pour vérifier que tous les modules Julia fonctionnent correctement.

Usage:
julia test_modules.jl
"""

println("🧪 TEST DES MODULES BACCARAT ENTROPY ANALYZER")
println("=" ^ 50)

# Test 1: Chargement du package principal
println("\n1️⃣ Test du chargement du package principal...")
try
    include("BaccaratEntropyAnalyzer.jl")
    using .BaccaratEntropyAnalyzer
    println("✅ Package principal chargé avec succès")
catch e
    println("❌ Erreur lors du chargement du package principal:")
    println(e)
    exit(1)
end

# Test 2: Vérification des modules individuels
println("\n2️⃣ Test des modules individuels...")

modules_to_test = [
    ("BaccaratEntropyAnalyzer", "BaccaratEntropyAnalyzer/BaccaratEntropyAnalyzer.jl"),
    ("INDEX5Calculator", "INDEX5Calculator/INDEX5Calculator.jl"),
    ("INDEX5DifferentialAnalyzer", "INDEX5DifferentialAnalyzer/INDEX5DifferentialAnalyzer.jl"),
    ("INDEX5PredictionValidator", "INDEX5PredictionValidator/INDEX5PredictionValidator.jl"),
    ("INDEX5PredictiveDifferentialTable", "INDEX5PredictiveDifferentialTable/INDEX5PredictiveDifferentialTable.jl"),
    ("INDEX5PredictiveScoreCalculator", "INDEX5PredictiveScoreCalculator/INDEX5PredictiveScoreCalculator.jl"),
    ("INDEX5PredictiveScoreTable", "INDEX5PredictiveScoreTable/INDEX5PredictiveScoreTable.jl"),
    ("INDEX5Predictor", "INDEX5Predictor/INDEX5Predictor.jl"),
    ("General", "General/General.jl")
]

for (module_name, module_path) in modules_to_test
    try
        if isfile(module_path)
            println("  ✅ $module_name: Fichier trouvé")
        else
            println("  ❌ $module_name: Fichier manquant ($module_path)")
        end
    catch e
        println("  ❌ $module_name: Erreur - $e")
    end
end

# Test 3: Test de base des fonctionnalités
println("\n3️⃣ Test des fonctionnalités de base...")

try
    # Test de création d'un analyseur
    analyzer = BEA.BaccaratEntropyAnalyzer()
    println("  ✅ BaccaratEntropyAnalyzer: Instance créée")
    
    # Test de création d'un calculateur
    calculator = I5C.INDEX5Calculator(analyzer)
    println("  ✅ INDEX5Calculator: Instance créée")
    
    # Test de création d'un analyseur différentiel
    diff_analyzer = I5DA.INDEX5DifferentialAnalyzer()
    println("  ✅ INDEX5DifferentialAnalyzer: Instance créée")
    
    # Test de création d'un validateur
    validator = I5PV.INDEX5PredictionValidator()
    println("  ✅ INDEX5PredictionValidator: Instance créée")
    
    # Test de création d'un prédicteur
    predictor = I5P.INDEX5Predictor()
    println("  ✅ INDEX5Predictor: Instance créée")
    
    println("  ✅ Toutes les instances de base créées avec succès")
    
catch e
    println("  ❌ Erreur lors des tests de base:")
    println("     $e")
end

# Test 4: Test de la fonction principale (si disponible)
println("\n4️⃣ Test de la fonction principale...")
try
    if isdefined(Main, :main)
        println("  ✅ Fonction main() disponible")
        println("  ℹ️  Pour lancer l'interface interactive, utilisez: main()")
    else
        println("  ⚠️  Fonction main() non disponible dans le scope global")
        println("  ℹ️  Utilisez: General.main() ou BaccaratEntropyAnalyzer.main()")
    end
catch e
    println("  ❌ Erreur lors du test de la fonction principale:")
    println("     $e")
end

println("\n🎉 TESTS TERMINÉS")
println("=" ^ 50)
println("📋 Résumé:")
println("   • Package principal: Chargé")
println("   • Modules individuels: Vérifiés")
println("   • Instances de base: Créées")
println("   • Prêt pour utilisation!")
println()
println("🚀 Pour lancer l'analyse interactive:")
println("   julia> using BaccaratEntropyAnalyzer")
println("   julia> main()")
