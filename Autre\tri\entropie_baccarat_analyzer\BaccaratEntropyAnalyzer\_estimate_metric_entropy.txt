# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 516 à 548
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _estimate_metric_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Float64

Estime l'entropie métrique h_μ(T) selon Kolmogorov-Sinai.
CORRECTION EXPERTE: Calcul rigoureux de la limite h_μ(T) = lim H(n)/n.

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md
Formule: h_μ(T) = lim_{n→∞} (1/n) H_μ(⋁ᵢ₌₀ⁿ⁻¹ T⁻ⁱα)
"""
function _estimate_metric_entropy(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Float64
    if length(sequence) < 3
        return 0.0
    end

    # CORRECTION: Calculer les entropies de blocs sans normalisation par longueur
    block_entropies_raw = _calculate_block_entropies_raw(analyzer, sequence, max_length)

    if length(block_entropies_raw) < 2
        return 0.0
    end

    # CORRECTION EXPERTE: Calcul rigoureux de h_μ = lim_{n→∞} H(n)/n
    # Formule correcte: h_μ(T) = lim H(n)/n selon Kolmogorov-Sinai
    h_metric_estimates = Float64[]
    for (i, entropy) in enumerate(block_entropies_raw)
        block_length = i  # Longueur du bloc (commence à 1)
        if block_length > 0
            h_estimate = entropy / block_length  # H(n)/n
            push!(h_metric_estimates, h_estimate)
        end
    end

    # Prendre la dernière estimation comme approximation de la limite
    if !isempty(h_metric_estimates)
        h_metric = max(0.0, h_metric_estimates[end])
    else
        h_metric = 0.0
    end

    return h_metric
end