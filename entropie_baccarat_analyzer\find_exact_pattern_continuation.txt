# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1793 à 1812
# Type: Méthode de la classe INDEX5Predictor

"""
    find_exact_pattern_continuation(predictor::INDEX5Predictor, pattern::Vector{String}, sequence_history::Vector{String})::Union{String, Nothing}

Trouve les continuations d'un pattern exact dans l'historique
"""
function find_exact_pattern_continuation(predictor::INDEX5Predictor, pattern::Vector{String}, sequence_history::Vector{String})::Union{String, Nothing}
    continuations = Dict{String, Int}()
    pattern_len = length(pattern)

    for i in 1:(length(sequence_history) - pattern_len)
        if sequence_history[i:i+pattern_len-1] == pattern
            # Si il y a une continuation après ce pattern
            if i + pattern_len <= length(sequence_history)
                next_value = sequence_history[i + pattern_len]
                continuations[next_value] = get(continuations, next_value, 0) + 1
            end
        end
    end

    if !isempty(continuations)
        # Retourner la continuation la plus fréquente
        best_continuation = argmax(continuations)
        return best_continuation
    end

    return nothing
end