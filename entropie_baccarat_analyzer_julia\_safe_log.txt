# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 123 à 134
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _safe_log(analyzer::BaccaratEntropyAnalyzer, x::Vector{Float64})::Vector{Float64}

🔒 SÉCURITÉ - Calcul sécurisé du logarithme avec gestion de log(0)

Évite les erreurs mathématiques en remplaçant les valeurs nulles ou négatives
par epsilon avant le calcul logarithmique.

Référence: entropie/cours_entropie/ressources/implementations_python.py
"""
function _safe_log(analyzer::BaccaratEntropyAnalyzer, x::Vector{Float64})::Vector{Float64}
    x_safe = [val <= 0 ? analyzer.epsilon : val for val in x]
    return [log(analyzer.base, val) for val in x_safe]
end