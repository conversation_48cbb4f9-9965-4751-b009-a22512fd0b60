# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 550 à 598
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    analyze_single_game(analyzer::BaccaratEntropyAnalyzer, game_data::Dict{String, Any}, game_id::Union{String, Nothing}=nothing)::Dict{String, Any}

Analyse complète d'une seule partie selon les méthodes avancées d'entropie.

Référence: entropie/cours_entropie/niveau_expert/01_entropie_metrique.md

# Arguments
- `game_data::Dict{String, Any}`: Données de la partie
- `game_id::Union{String, Nothing}`: Identifiant de la partie (optionnel)

# Returns
- `Dict{String, Any}`: Résultats d'analyse complets avec entropie métrique
"""
function analyze_single_game(analyzer::BaccaratEntropyAnalyzer, game_data::Dict{String, Any}, game_id::Union{String, Nothing}=nothing)::Dict{String, Any}
    sequence = extract_index5_sequence(analyzer, game_data)

    if isempty(sequence)
        return Dict{String, Any}("error" => "Aucune séquence INDEX5 trouvée")
    end

    # Nouvelle méthode : entropie de blocs et métrique
    entropy_evolution = calculate_block_entropy_evolution(analyzer, sequence, max_block_length=4)

    if isempty(entropy_evolution)
        return Dict{String, Any}("error" => "Impossible de calculer l'évolution d'entropie")
    end

    # Extraction des métriques finales
    final_analysis = entropy_evolution[end]

    # Calcul de la complexité de la séquence
    complexity_metrics = _calculate_sequence_complexity(analyzer, sequence)

    # Positions d'intérêt
    max_metric_entropy_position = 0
    max_conditional_entropy_position = 0

    if !isempty(entropy_evolution)
        max_metric_entropy_position = argmax([x["metric_entropy"] for x in entropy_evolution])
        max_conditional_entropy_position = argmax([x["conditional_entropy"] for x in entropy_evolution])
        max_metric_entropy_position = entropy_evolution[max_metric_entropy_position]["position"]
        max_conditional_entropy_position = entropy_evolution[max_conditional_entropy_position]["position"]
    end

    return Dict{String, Any}(
        "game_id" => game_id !== nothing ? game_id : "Unknown",
        "sequence_length" => length(sequence),
        "full_sequence" => sequence,
        "entropy_evolution" => entropy_evolution,

        # Métriques finales (nouvelle approche)
        "final_metric_entropy" => final_analysis["metric_entropy"],
        "final_conditional_entropy" => final_analysis["conditional_entropy"],
        "final_entropy_rate" => final_analysis["entropy_rate"],
        "final_simple_entropy" => final_analysis["simple_entropy"],  # Ancienne méthode

        # Analyse de complexité
        "complexity_metrics" => complexity_metrics,

        # Positions d'intérêt
        "max_metric_entropy_position" => max_metric_entropy_position,
        "max_conditional_entropy_position" => max_conditional_entropy_position
    )
end