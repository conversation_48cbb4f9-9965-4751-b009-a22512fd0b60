# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2563 à 2596
# Type: Méthode de la classe INDEX5PredictiveScoreTable

"""
    generate_predictive_score_table(table::INDEX5PredictiveScoreTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, precomputed_differentials::Union{Dict, Nothing}=nothing)::String

Génère le tableau prédictif complet avec SCORES divisé en deux parties
CORRECTION : Utilise les différentiels pré-calculés pour garantir la synchronisation parfaite
"""
function generate_predictive_score_table(table::INDEX5PredictiveScoreTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, precomputed_differentials::Union{Dict, Nothing}=nothing)::String
    if isempty(sequence) || isempty(evolution)
        return "❌ Données insuffisantes pour générer le tableau prédictif"
    end

    # Générer la première partie (Mains 1-30)
    table_part1 = generate_predictive_score_table_part(table, sequence, evolution, analyzer, 1, 30, 1, precomputed_differentials)

    # Générer la deuxième partie (Mains 31-60)
    table_part2 = generate_predictive_score_table_part(table, sequence, evolution, analyzer, 31, 60, 2, precomputed_differentials)

    # Combiner les deux parties avec la légende
    complete_table = table_part1 * "\n\n" * table_part2 * """

📋 LÉGENDE DU TABLEAU PRÉDICTIF AVEC SCORES :
• SCORE = (DiffC + EntG) / (DiffT + DivEG)
• DiffC = DiffCond (Différentiel Entropie Conditionnelle)
• DiffT = DiffTaux (Différentiel Taux d'Entropie)
• DivEG = DiffDivEntropG (Différentiel Diversité Entropique)
• EntG = DiffEntropG (Différentiel Entropie Générale)
• Seules les 9 valeurs INDEX5 respectant les règles INDEX1 sont calculées
• N/A = Valeur non calculable (ne respecte pas les règles INDEX1)
• INF = Score infini (dénominateur = 0)
• --- = Main non disponible dans cette partie

🔄 RÈGLES INDEX1 DÉTERMINISTES :
• Si INDEX2 = C : INDEX1 s'inverse (0→1, 1→0)
• Si INDEX2 = A ou B : INDEX1 se conserve (0→0, 1→1)
"""

    return complete_table
end