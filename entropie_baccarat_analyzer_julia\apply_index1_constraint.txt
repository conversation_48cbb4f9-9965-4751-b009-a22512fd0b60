# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1965 à 2001
# Type: Méthode de la classe INDEX5Predictor

"""
    apply_index1_constraint(predictor::INDEX5Predictor, current_index5::String, predicted_index5::String)::String

Applique la contrainte INDEX1 déterministe à la prédiction
Règles INDEX1:
- Si INDEX2=C: INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2=A ou B: INDEX1 se conserve (0→0, 1→1)
"""
function apply_index1_constraint(predictor::INDEX5Predictor, current_index5::String, predicted_index5::String)::String
    if isempty(current_index5) || isempty(predicted_index5)
        return predicted_index5
    end

    try
        # Extraire INDEX1 et INDEX2 actuels
        current_parts = split(current_index5, '_')
        current_index1 = parse(Int, current_parts[1])
        current_index2 = current_parts[2]

        # Calculer INDEX1 obligatoire pour n+1 selon les règles déterministes
        if current_index2 == "C"
            required_index1 = 1 - current_index1  # Inversion obligatoire
        else  # A ou B
            required_index1 = current_index1      # Conservation obligatoire
        end

        # Extraire INDEX2 et INDEX3 de la prédiction
        predicted_parts = split(predicted_index5, '_')
        if length(predicted_parts) >= 3
            predicted_index2 = predicted_parts[2]
            predicted_index3 = predicted_parts[3]

            # Construire INDEX5 contraint avec INDEX1 déterministe
            constrained_index5 = "$(required_index1)_$(predicted_index2)_$(predicted_index3)"
            return constrained_index5
        end

    catch e
        # En cas d'erreur, retourner la prédiction originale
        if isa(e, BoundsError) || isa(e, ArgumentError)
            # Continue to return original prediction
        else
            rethrow(e)
        end
    end

    return predicted_index5
end