# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 631 à 658
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _approximate_lz_complexity(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Int

Approximation de la complexité de Lempel-Ziv.

Référence: entropie/cours_entropie/niveau_intermediaire/02_codage_source.md
"""
function _approximate_lz_complexity(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Int
    if isempty(sequence)
        return 0
    end

    dictionary = Set{Tuple}()
    i = 1
    complexity = 0

    while i <= length(sequence)
        # Chercher le plus long préfixe non vu
        found = false
        for length in 1:(length(sequence) - i + 1)
            substring = Tuple(sequence[i:i+length-1])
            if substring ∉ dictionary
                push!(dictionary, substring)
                complexity += 1
                i += length
                found = true
                break
            end
        end

        if !found
            # Tous les préfixes sont dans le dictionnaire
            i += 1
            complexity += 1
        end
    end

    return complexity
end