# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2049 à 2136
# Type: Méthode de la classe INDEX5Predictor

"""
    predict_next_index5(predictor::INDEX5Predictor, sequence_history::Vector{String}, all_metrics::Dict{String, Any})::Union{Dict{String, Any}, Nothing}

Prédicteur INDEX5 principal utilisant fusion multi-algorithmes
AVEC CONTRAINTE INDEX1 DÉTERMINISTE APPLIQUÉE AVANT LE VOTE
"""
function predict_next_index5(predictor::INDEX5Predictor, sequence_history::Vector{String}, all_metrics::Dict{String, Any})::Union{Dict{String, Any}, Nothing}
    if isempty(sequence_history) || isempty(all_metrics)
        return nothing
    end

    # ÉTAPE 1: Déterminer INDEX1 obligatoire selon les règles déterministes
    current_index5 = sequence_history[end]
    required_index1 = calculate_required_index1(predictor, current_index5)

    if required_index1 === nothing
        return nothing
    end

    # ÉTAPE 2: Obtenir la liste des INDEX5 valides
    valid_index5_values = get_valid_index5_values(predictor, required_index1)

    # ÉTAPE 3: Évaluation de la prédictibilité actuelle
    conditional_entropy = get(all_metrics, "conditional_entropy", 6.2192)
    current_predictability = max(0.0, (6.2192 - conditional_entropy) / 6.2192)

    # ÉTAPE 4: Sélection de la stratégie optimale
    weight_deterministic, weight_bayesian, weight_frequency = if current_predictability > 0.40  # Très prévisible
        (0.7, 0.2, 0.1)
    elseif current_predictability > 0.30  # Prévisible
        (0.5, 0.3, 0.2)
    else  # Moins prévisible
        (0.3, 0.5, 0.2)
    end

    # ÉTAPE 5: Calcul des prédictions FILTRÉES par chaque méthode
    pred_deterministic_raw = predict_deterministic_patterns(predictor, sequence_history, all_metrics)
    pred_bayesian_raw = predict_bayesian_theoretical(predictor, sequence_history, all_metrics)
    pred_frequency_raw = predict_frequency_based(predictor, sequence_history, all_metrics)

    # FILTRAGE : Ne garder que les prédictions qui respectent INDEX1
    pred_deterministic = filter_prediction_by_constraint(predictor, pred_deterministic_raw, valid_index5_values)
    pred_bayesian = filter_prediction_by_constraint(predictor, pred_bayesian_raw, valid_index5_values)
    pred_frequency = filter_prediction_by_constraint(predictor, pred_frequency_raw, valid_index5_values)

    # ÉTAPE 6: Fusion pondérée des prédictions VALIDES uniquement
    predictions = Tuple{String, String, Float64}[]
    if pred_deterministic !== nothing
        push!(predictions, ("DETERMINISTIC", pred_deterministic, weight_deterministic))
    end
    if pred_bayesian !== nothing
        push!(predictions, ("BAYESIAN", pred_bayesian, weight_bayesian))
    end
    if pred_frequency !== nothing
        push!(predictions, ("FREQUENCY", pred_frequency, weight_frequency))
    end

    # ÉTAPE 7: Si aucune prédiction valide → WAIT
    if isempty(predictions)
        return Dict{String, Any}(
            "predicted_index5" => "WAIT",
            "confidence" => 0.0,
            "predictability_score" => current_predictability,
            "contributing_methods" => String[],
            "constraint_applied" => true,
            "original_prediction" => "WAIT",
            "reason" => "Aucune prédiction valide pour INDEX1=$required_index1"
        )
    end

    # ÉTAPE 8: Vote pondéré sur les prédictions valides
    vote_weights = Dict{String, Float64}()
    for (method, pred, weight) in predictions
        vote_weights[pred] = get(vote_weights, pred, 0.0) + weight
    end

    # Retourner la prédiction avec le plus fort poids
    best_prediction = argmax(vote_weights)
    final_prediction = best_prediction

    # RESTAURATION: Utiliser le poids pondéré cumulé comme dans l'ancien code
    weighted_confidence = vote_weights[best_prediction]

    return Dict{String, Any}(
        "predicted_index5" => final_prediction,
        "confidence" => weighted_confidence,
        "predictability_score" => current_predictability,
        "contributing_methods" => [p[1] for p in predictions if p[2] == final_prediction],
        "constraint_applied" => true,
        "original_prediction" => final_prediction,
        "required_index1" => required_index1
    )
end