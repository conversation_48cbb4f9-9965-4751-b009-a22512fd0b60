# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2711 à 2756
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

"""
    calculate_simulated_metrics(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, position::Int, possible_index5::String, analyzer::BaccaratEntropyAnalyzer)::Dict{String, Float64}

Calcule les métriques pour une séquence simulée avec possible_index5 ajouté
"""
function calculate_simulated_metrics(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, position::Int, possible_index5::String, analyzer::BaccaratEntropyAnalyzer)::Dict{String, Float64}
    if position > length(sequence)
        return Dict{String, Float64}()
    end

    # Créer séquence simulée avec la valeur possible ajoutée
    simulated_sequence = vcat(sequence[1:position], [possible_index5])

    # Calculer les métriques de base pour la nouvelle position
    metrics = Dict{String, Float64}()

    try
        # Entropie conditionnelle
        if length(simulated_sequence) >= 2
            metrics["conditional_entropy"] = _calculate_conditional_entropy(analyzer, simulated_sequence)
        else
            metrics["conditional_entropy"] = 0.0
        end

        # Entropie simple (Shannon)
        # Calculer les fréquences de la séquence simulée
        counts = Dict{String, Int}()
        for value in simulated_sequence
            counts[value] = get(counts, value, 0) + 1
        end
        total = length(simulated_sequence)
        probabilities = [counts[value] / total for value in keys(counts)]
        metrics["simple_entropy"] = _calculate_shannon_entropy(analyzer, probabilities)

        # Entropie théorique (AEP)
        metrics["simple_entropy_theoretical"] = _calculate_sequence_entropy_aep(analyzer, simulated_sequence)

        # Taux d'entropie (approximation)
        if length(simulated_sequence) >= 3
            block_entropies = _calculate_block_entropies(analyzer, simulated_sequence, min(5, length(simulated_sequence)))
            if !isempty(block_entropies)
                metrics["entropy_rate"] = block_entropies[end]
            else
                metrics["entropy_rate"] = metrics["simple_entropy"]
            end
        else
            metrics["entropy_rate"] = metrics["simple_entropy"]
        end

    catch e
        # En cas d'erreur, retourner des valeurs par défaut
        metrics = Dict{String, Float64}(
            "conditional_entropy" => 0.0,
            "simple_entropy" => 0.0,
            "simple_entropy_theoretical" => 0.0,
            "entropy_rate" => 0.0
        )
    end

    return metrics
end