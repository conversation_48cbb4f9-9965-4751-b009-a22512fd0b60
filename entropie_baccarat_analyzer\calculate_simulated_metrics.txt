# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2711 à 2756
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

    def calculate_simulated_metrics(self, sequence, position, possible_index5, analyzer):
        """
        Calcule les métriques pour une séquence simulée avec possible_index5 ajouté
        """
        if position >= len(sequence):
            return {}

        # Créer séquence simulée avec la valeur possible ajoutée
        simulated_sequence = sequence[:position+1] + [possible_index5]

        # Calculer les métriques de base pour la nouvelle position
        metrics = {}

        try:
            # Entropie conditionnelle
            if len(simulated_sequence) >= 2:
                metrics['conditional_entropy'] = analyzer._calculate_conditional_entropy(simulated_sequence)
            else:
                metrics['conditional_entropy'] = 0.0

            # Entropie simple (Shannon)
            metrics['simple_entropy'] = analyzer._calculate_shannon_entropy(simulated_sequence)

            # Entropie théorique (AEP)
            metrics['simple_entropy_theoretical'] = analyzer._calculate_sequence_entropy_aep(simulated_sequence)

            # Taux d'entropie (approximation)
            if len(simulated_sequence) >= 3:
                block_entropies = analyzer._calculate_block_entropies(simulated_sequence, min(5, len(simulated_sequence)))
                if block_entropies:
                    metrics['entropy_rate'] = block_entropies[-1]
                else:
                    metrics['entropy_rate'] = metrics['simple_entropy']
            else:
                metrics['entropy_rate'] = metrics['simple_entropy']

        except Exception as e:
            # En cas d'erreur, retourner des valeurs par défaut
            metrics = {
                'conditional_entropy': 0.0,
                'simple_entropy': 0.0,
                'simple_entropy_theoretical': 0.0,
                'entropy_rate': 0.0
            }

        return metrics