# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 163 à 187
# Type: Méthode de la classe BaccaratEntropyAnalyzer

    def _calculate_shannon_entropy(self, probabilities: List[float]) -> float:
        """
        📊 SHANNON - Calcule l'entropie de Shannon: H(X) = -∑ p(x) log₂ p(x)

        Formule fondamentale de la théorie de l'information pour mesurer
        l'incertitude d'une distribution de probabilités.

        Référence: entropie/cours_entropie/niveau_debutant/02_formule_shannon.md

        Args:
            probabilities: Liste des probabilités

        Returns:
            Entropie de Shannon en bits
        """
        p = self._validate_probabilities(probabilities)
        
        # Calcul avec gestion de 0*log(0) = 0
        log_p = self._safe_log(p)
        entropy_terms = p * log_p
        
        # Remplace NaN par 0 (cas 0*log(0))
        entropy_terms = np.where(p == 0, 0, entropy_terms)
        
        return -np.sum(entropy_terms)