# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1755 à 1768
# Type: Méthode de la classe INDEX5Predictor
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

"""
    INDEX5Predictor()

Initialisation du prédicteur INDEX5
"""
mutable struct INDEX5Predictor
    THEORETICAL_PROBS::Dict{String, Float64}

    function INDEX5Predictor()
        # Probabilités théoriques INDEX5
        THEORETICAL_PROBS = Dict{String, Float64}(
            "0_A_BANKER" => 0.085136, "1_A_BANKER" => 0.086389,
            "0_B_BANKER" => 0.064676, "1_B_BANKER" => 0.065479,
            "0_C_BANKER" => 0.077903, "1_C_BANKER" => 0.078929,
            "0_A_PLAYER" => 0.085240, "1_A_PLAYER" => 0.086361,
            "0_B_PLAYER" => 0.076907, "1_B_PLAYER" => 0.077888,
            "0_C_PLAYER" => 0.059617, "1_C_PLAYER" => 0.060352,
            "0_A_TIE" => 0.017719, "1_A_TIE" => 0.017978,
            "0_B_TIE" => 0.016281, "1_B_TIE" => 0.016482,
            "0_C_TIE" => 0.013241, "1_C_TIE" => 0.013423
        )

        new(THEORETICAL_PROBS)
    end
end