# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3170 à 3210
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    get_detailed_report(validator::INDEX5PredictionValidator)::String

Retourne un rapport détaillé des prédictions
"""
function get_detailed_report(validator::INDEX5PredictionValidator)::String
    stats = get_accuracy_stats(validator)

    report = """
🎯 VALIDATION DES PRÉDICTIONS INDEX5 - COMPARAISON INDEX3
═══════════════════════════════════════════════════════

📊 STATISTIQUES GLOBALES:
• Prédictions correctes: $(stats["correct_predictions"])
• Total prédictions: $(stats["total_predictions"])
• Taux de réussite: $(round(stats["accuracy_percentage"], digits=2))%
• Ratio: $(stats["accuracy_ratio"])

🎯 STATISTIQUES HAUTE CONFIANCE (≥ 60% poids pondéré):
• Prédictions correctes (≥60%): $(stats["correct_predictions_high_confidence"])
• Total prédictions (≥60%): $(stats["total_predictions_high_confidence"])
• Taux de réussite (≥60%): $(round(stats["accuracy_percentage_high_confidence"], digits=2))%
• Ratio (≥60%): $(stats["accuracy_ratio_high_confidence"])

🔍 MÉTHODE DE VALIDATION:
• Format INDEX5: INDEX1_INDEX2_INDEX3
• Validation: INDEX3_prédit = INDEX3_réel
• Exemple: 0_A_BANKER → INDEX3 = BANKER
• Confiance haute: Score ≥ 0.60 (60%)
"""

    if !isempty(validator.prediction_details)
        report *= "\n📋 DÉTAIL DES PRÉDICTIONS:\n"
        report *= "Position | Prédiction → Réalité | INDEX3 Prédit → INDEX3 Réel | Confiance | Résultat\n"
        report *= "---------|---------------------|---------------------------|-----------|----------\n"

        # Afficher les 10 dernières prédictions
        last_details = length(validator.prediction_details) > 10 ?
                      validator.prediction_details[end-9:end] :
                      validator.prediction_details

        for detail in last_details
            result_symbol = detail["is_correct"] ? "✅" : "❌"
            confidence_symbol = get(detail, "is_high_confidence", false) ? "🎯" : "📊"
            confidence_display = @sprintf("%.2f", get(detail, "confidence", 0.0))

            report *= @sprintf("Main %2d  | %12s → %12s | %6s → %6s | %s%5s | %s\n",
                detail["position"],
                detail["predicted_index5"],
                detail["actual_index5"],
                detail["predicted_index3"],
                detail["actual_index3"],
                confidence_symbol,
                confidence_display,
                result_symbol)
        end
    end

    return report
end