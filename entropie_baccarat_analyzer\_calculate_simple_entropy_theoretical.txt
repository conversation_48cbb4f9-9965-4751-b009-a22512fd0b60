# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 455 à 460
# Type: Méthode de la classe BaccaratEntropyAnalyzer

    def _calculate_simple_entropy_theoretical(self, sequence: List[str]) -> float:
        """
        Calcule l'entropie théorique simple - WRAPPER vers la méthode AEP unifiée.
        Maintenu pour compatibilité avec le code existant.
        """
        return self._calculate_sequence_entropy_aep(sequence)