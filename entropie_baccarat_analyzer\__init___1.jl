# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1187 à 1208
# Type: Méthode de la classe INDEX5Calculator
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

"""
    INDEX5Calculator(analyzer=nothing)

🔧 INITIALISATION - Configuration du calculateur INDEX5

Initialise les probabilités théoriques et la référence vers l'analyzer
pour accéder aux méthodes d'entropie AEP.
"""
mutable struct INDEX5Calculator
    analyzer::Union{BaccaratEntropyAnalyzer, Nothing}
    THEORETICAL_PROBS::Dict{String, Float64}
    
    function INDEX5Calculator(analyzer=nothing)
        # Référence vers l'analyzer pour accéder aux méthodes d'entropie
        
        # Probabilités théoriques INDEX5 (adaptées depuis BaccaratEntropyAnalyzer)
        THEORETICAL_PROBS = Dict{String, Float64}(
            "0_A_BANKER" => 0.085136, "1_A_BANKER" => 0.086389,
            "0_B_BANKER" => 0.064676, "1_B_BANKER" => 0.065479,
            "0_C_BANKER" => 0.077903, "1_C_BANKER" => 0.078929,
            "0_A_PLAYER" => 0.085240, "1_A_PLAYER" => 0.086361,
            "0_B_PLAYER" => 0.076907, "1_B_PLAYER" => 0.077888,
            "0_C_PLAYER" => 0.059617, "1_C_PLAYER" => 0.060352,
            "0_A_TIE" => 0.017719, "1_A_TIE" => 0.017978,
            "0_B_TIE" => 0.016281, "1_B_TIE" => 0.016482,
            "0_C_TIE" => 0.013241, "1_C_TIE" => 0.013423
        )
        
        new(analyzer, THEORETICAL_PROBS)
    end
end
