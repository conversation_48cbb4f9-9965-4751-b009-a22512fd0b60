# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2165 à 2179
# Type: Méthode de la classe INDEX5Predictor
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

"""
    find_pattern_continuations(predictor::INDEX5Predictor, pattern::Vector{String}, sequence_history::Vector{String})::Dict{String, Int}

Trouve toutes les continuations d'un pattern dans l'historique
"""
function find_pattern_continuations(predictor::INDEX5Predictor, pattern::Vector{String}, sequence_history::Vector{String})::Dict{String, Int}
    continuations = Dict{String, Int}()
    pattern_len = length(pattern)

    for i in 1:(length(sequence_history) - pattern_len)
        if sequence_history[i:i+pattern_len-1] == pattern
            # Si il y a une continuation après ce pattern
            if i + pattern_len <= length(sequence_history)
                next_value = sequence_history[i + pattern_len]
                continuations[next_value] = get(continuations, next_value, 0) + 1
            end
        end
    end

    return continuations
end