# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =
# FICHIER CONSOLIDÉ POUR LA CLASSE: INDEX5PredictionValidator
# Généré automatiquement - 7 fichiers sources
# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =# =

# ------------------------------------------------------------
# FICHIER SOURCE: __init___7.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3033 à 3039
# Type: Méthode de la classe INDEX5PredictionValidator
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

"""
    INDEX5PredictionValidator()

Initialisation du validateur de prédictions
"""
mutable struct INDEX5PredictionValidator
    correct_predictions::Int
    total_predictions::Int
    correct_predictions_high_confidence::Int  # Nouveau compteur pour poids >= 60%
    total_predictions_high_confidence::Int    # Total prédictions avec poids >= 60%
    prediction_details::Vector{Any}

    function INDEX5PredictionValidator()
        new(0, 0, 0, 0, Any[])
    end
end


# ------------------------------------------------------------
# FICHIER SOURCE: extract_confidence.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3067 à 3080
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    extract_confidence(validator::INDEX5PredictionValidator, predicted_index5::Union{String, Nothing})::Float64

Extrait le score de confiance d'une prédiction
Format: INDEX5(0.XX) → retourne 0.XX comme float
"""
function extract_confidence(validator::INDEX5PredictionValidator, predicted_index5::Union{String, Nothing})::Float64
    if predicted_index5 === nothing || !occursin('(', predicted_index5)
        return 0.0
    end

    try
        # Extraire la partie entre parenthèses
        confidence_part = split(split(predicted_index5, '(')[2], ')')[1]
        return parse(Float64, confidence_part)
    catch e
        if isa(e, BoundsError) || isa(e, ArgumentError)
            return 0.0
        else
            rethrow(e)
        end
    end
end


# ------------------------------------------------------------
# FICHIER SOURCE: extract_index3.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3041 à 3065
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    extract_index3(validator::INDEX5PredictionValidator, index5_value::Union{String, Nothing})::Union{String, Nothing}

Extrait INDEX3 d'une valeur INDEX5
Format: INDEX1_INDEX2_INDEX3 → retourne INDEX3
"""
function extract_index3(validator::INDEX5PredictionValidator, index5_value::Union{String, Nothing})::Union{String, Nothing}
    if index5_value === nothing || index5_value == "N/A"
        return nothing
    end

    # Nettoyer la valeur (enlever score de confiance si présent)
    clean_value = occursin('(', string(index5_value)) ? split(string(index5_value), '(')[1] : string(index5_value)

    # Diviser par underscore et prendre le dernier élément (INDEX3)
    parts = split(clean_value, '_')
    if length(parts) >= 3
        index3 = parts[3]  # INDEX3 (BANKER, PLAYER, TIE)

        # Normaliser les abréviations vers les formes complètes
        if index3 == "BANK"
            return "BANKER"
        elseif index3 == "PLAY"
            return "PLAYER"
        else
            return index3  # TIE reste TIE
        end
    end

    return nothing
end


# ------------------------------------------------------------
# FICHIER SOURCE: get_accuracy_stats.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3136 à 3168
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    get_accuracy_stats(validator::INDEX5PredictionValidator)::Dict{String, Any}

Retourne les statistiques de précision
"""
function get_accuracy_stats(validator::INDEX5PredictionValidator)::Dict{String, Any}
    if validator.total_predictions == 0
        return Dict{String, Any}(
            "correct_predictions" => 0,
            "total_predictions" => 0,
            "accuracy_percentage" => 0.0,
            "accuracy_ratio" => "0/0",
            "correct_predictions_high_confidence" => 0,
            "total_predictions_high_confidence" => 0,
            "accuracy_percentage_high_confidence" => 0.0,
            "accuracy_ratio_high_confidence" => "0/0"
        )
    end

    accuracy = (validator.correct_predictions / validator.total_predictions) * 100

    # Calculer la précision pour les prédictions haute confiance (>= 60% poids pondéré)
    accuracy_high_confidence = 0.0
    if validator.total_predictions_high_confidence > 0
        accuracy_high_confidence = (validator.correct_predictions_high_confidence / validator.total_predictions_high_confidence) * 100
    end

    return Dict{String, Any}(
        "correct_predictions" => validator.correct_predictions,
        "total_predictions" => validator.total_predictions,
        "accuracy_percentage" => accuracy,
        "accuracy_ratio" => "$(validator.correct_predictions)/$(validator.total_predictions)",
        "correct_predictions_high_confidence" => validator.correct_predictions_high_confidence,
        "total_predictions_high_confidence" => validator.total_predictions_high_confidence,
        "accuracy_percentage_high_confidence" => accuracy_high_confidence,
        "accuracy_ratio_high_confidence" => "$(validator.correct_predictions_high_confidence)/$(validator.total_predictions_high_confidence)"
    )
end


# ------------------------------------------------------------
# FICHIER SOURCE: get_detailed_report.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3170 à 3210
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    get_detailed_report(validator::INDEX5PredictionValidator)::String

Retourne un rapport détaillé des prédictions
"""
function get_detailed_report(validator::INDEX5PredictionValidator)::String
    stats = get_accuracy_stats(validator)

    report = """
🎯 VALIDATION DES PRÉDICTIONS INDEX5 - COMPARAISON INDEX3
═══════════════════════════════════════════════════════

📊 STATISTIQUES GLOBALES:
• Prédictions correctes: $(stats["correct_predictions"])
• Total prédictions: $(stats["total_predictions"])
• Taux de réussite: $(round(stats["accuracy_percentage"], digits=2))%
• Ratio: $(stats["accuracy_ratio"])

🎯 STATISTIQUES HAUTE CONFIANCE (≥ 60% poids pondéré):
• Prédictions correctes (≥60%): $(stats["correct_predictions_high_confidence"])
• Total prédictions (≥60%): $(stats["total_predictions_high_confidence"])
• Taux de réussite (≥60%): $(round(stats["accuracy_percentage_high_confidence"], digits=2))%
• Ratio (≥60%): $(stats["accuracy_ratio_high_confidence"])

🔍 MÉTHODE DE VALIDATION:
• Format INDEX5: INDEX1_INDEX2_INDEX3
• Validation: INDEX3_prédit = INDEX3_réel
• Exemple: 0_A_BANKER → INDEX3 = BANKER
• Confiance haute: Score ≥ 0.60 (60%)
"""

    if !isempty(validator.prediction_details)
        report *= "\n📋 DÉTAIL DES PRÉDICTIONS:\n"
        report *= "Position | Prédiction → Réalité | INDEX3 Prédit → INDEX3 Réel | Confiance | Résultat\n"
        report *= "---------|---------------------|---------------------------|-----------|----------\n"

        # Afficher les 10 dernières prédictions
        last_details = length(validator.prediction_details) > 10 ?
                      validator.prediction_details[end-9:end] :
                      validator.prediction_details

        for detail in last_details
            result_symbol = detail["is_correct"] ? "✅" : "❌"
            confidence_symbol = get(detail, "is_high_confidence", false) ? "🎯" : "📊"
            confidence_display = @sprintf("%.2f", get(detail, "confidence", 0.0))

            report *= @sprintf("Main %2d  | %12s → %12s | %6s → %6s | %s%5s | %s\n",
                detail["position"],
                detail["predicted_index5"],
                detail["actual_index5"],
                detail["predicted_index3"],
                detail["actual_index3"],
                confidence_symbol,
                confidence_display,
                result_symbol)
        end
    end

    return report
end


# ------------------------------------------------------------
# FICHIER SOURCE: reset.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3212 à 3220
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    reset!(validator::INDEX5PredictionValidator)

Remet à zéro les compteurs
"""
function reset!(validator::INDEX5PredictionValidator)
    validator.correct_predictions = 0
    validator.total_predictions = 0
    validator.correct_predictions_high_confidence = 0
    validator.total_predictions_high_confidence = 0
    validator.prediction_details = Dict{String, Any}[]
end


# ------------------------------------------------------------
# FICHIER SOURCE: validate_prediction.txt
# ------------------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3082 à 3134
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    validate_prediction(validator::INDEX5PredictionValidator, predicted_index5::String, actual_index5::String, position::Int)::Union{Bool, Nothing}

Valide une prédiction en comparant les INDEX3
Ignore les prédictions "WAIT"
"""
function validate_prediction(validator::INDEX5PredictionValidator, predicted_index5::String, actual_index5::String, position::Int)::Union{Bool, Nothing}
    # Ignorer les prédictions WAIT
    if predicted_index5 == "WAIT"
        return nothing
    end

    # Extraire INDEX3 de la prédiction (le nettoyage est fait dans extract_index3)
    predicted_index3 = extract_index3(validator, predicted_index5)

    # Extraire INDEX3 de la valeur réelle
    actual_index3 = extract_index3(validator, actual_index5)

    # Vérifier si les deux INDEX3 sont valides
    if predicted_index3 !== nothing && actual_index3 !== nothing
        # RÈGLE TIE: Si réalité = TIE et prédiction ≠ TIE → NE PAS COMPTER
        if actual_index3 == "TIE" && predicted_index3 != "TIE"
            # Ne pas compter cette prédiction (ni valide ni invalide)
            return nothing
        end

        # Extraire le score de confiance
        confidence = extract_confidence(validator, predicted_index5)

        validator.total_predictions += 1
        is_correct = predicted_index3 == actual_index3

        if is_correct
            validator.correct_predictions += 1
        end

        # Compteur spécial pour poids pondéré >= 60% (seuil haute confiance)
        # RESTAURATION: Seuil basé sur poids pondéré cumulé comme dans l'ancien code
        if confidence >= 0.60
            validator.total_predictions_high_confidence += 1
            if is_correct
                validator.correct_predictions_high_confidence += 1
            end
        end

        # Enregistrer les détails
        push!(validator.prediction_details, Dict{String, Any}(
            "position" => position,
            "predicted_index5" => predicted_index5,
            "actual_index5" => actual_index5,
            "predicted_index3" => predicted_index3,
            "actual_index3" => actual_index3,
            "confidence" => confidence,
            "is_correct" => is_correct,
            "is_high_confidence" => confidence >= 0.60
        ))

        return is_correct
    end

    return nothing  # Prédiction non validable
end


# ============================================================
# FIN DU FICHIER CONSOLIDÉ POUR INDEX5PredictionValidator
# TOTAL: 7 fichiers sources traités
# ============================================================
