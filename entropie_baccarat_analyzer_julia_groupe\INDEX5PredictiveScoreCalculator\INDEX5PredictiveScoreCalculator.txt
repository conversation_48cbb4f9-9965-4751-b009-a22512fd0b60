# ============================================================
# CLASSE: INDEX5PredictiveScoreCalculator
# Fichier consolidé généré automatiquement
# Nombre de fichiers sources: 2
# ============================================================

# --------------------------------------------------
# FICHIER SOURCE: __init___4.txt
# --------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2405 à 2407
# Type: Méthode de la classe INDEX5PredictiveScoreCalculator
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

"""
    INDEX5PredictiveScoreCalculator()

Initialisation du calculateur de scores prédictifs
"""
mutable struct INDEX5PredictiveScoreCalculator
    function INDEX5PredictiveScoreCalculator()
        new()
    end
end


# --------------------------------------------------
# FICHIER SOURCE: calculate_predictive_score.txt
# --------------------------------------------------

# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2409 à 2428
# Type: Méthode de la classe INDEX5PredictiveScoreCalculator

"""
    calculate_predictive_score(calculator::INDEX5PredictiveScoreCalculator, diff_c::Float64, diff_t::Float64, div_eg::Float64, ent_g::Float64)::Float64

Calcule le score prédictif selon la formule:
SCORE = (DiffC + EntG) / (DiffT + DivEG)

# Arguments
- `diff_c::Float64`: Différentiel Entropie Conditionnelle
- `diff_t::Float64`: Différentiel Taux d'Entropie
- `div_eg::Float64`: Différentiel Diversité Entropique
- `ent_g::Float64`: Différentiel Entropie Générale

# Returns
- `Float64`: Score prédictif (peut être infini si dénominateur = 0)
"""
function calculate_predictive_score(calculator::INDEX5PredictiveScoreCalculator, diff_c::Float64, diff_t::Float64, div_eg::Float64, ent_g::Float64)::Float64
    denominator = diff_t + div_eg

    if denominator == 0
        return Inf
    else
        return (diff_c + ent_g) / denominator
    end
end


# ==================================================
# FIN DE LA CLASSE INDEX5PredictiveScoreCalculator
# TOTAL: 2 fichiers sources
# ==================================================
