# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 341 à 379
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _calculate_block_entropies(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Vector{Float64}

Calcule l'entropie pour des blocs de différentes longueurs.
CORRECTION: Utilise les probabilités théoriques pour les blocs de longueur 1

Référence: entropie/cours_entropie/ressources/implementations_python.py
Méthode de l'entropie métrique par blocs.
"""
function _calculate_block_entropies(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String}, max_length::Int)::Vector{Float64}
    if length(sequence) < 2
        return [0.0]
    end

    entropies = Float64[]

    for block_len in 1:min(max_length, length(sequence))
        if block_len == 1
            # CORRECTION AEP: Pour les blocs de longueur 1, calculer l'entropie de la séquence complète
            # selon la formule AEP au lieu d'utiliser toutes les probabilités théoriques
            block_entropy = _calculate_sequence_entropy_aep(analyzer, sequence)
            push!(entropies, block_entropy)
        else
            # Pour les blocs de longueur > 1, créer des sous-séquences et utiliser AEP
            block_sequences = Vector{String}[]
            for i in 1:(length(sequence) - block_len + 1)
                block_sequence = sequence[i:i+block_len-1]
                push!(block_sequences, block_sequence)
            end

            if isempty(block_sequences)
                push!(entropies, 0.0)
                continue
            end

            # CORRECTION AEP: Calculer l'entropie moyenne des blocs selon AEP
            total_entropy = 0.0
            for block_seq in block_sequences
                total_entropy += _calculate_sequence_entropy_aep(analyzer, block_seq)
            end

            block_entropy = !isempty(block_sequences) ? total_entropy / length(block_sequences) : 0.0
            push!(entropies, block_entropy)
        end
    end

    return entropies
end