# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1448 à 1487
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_multi_algorithm_consensus_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, all_metrics::Dict{String, Any})::Float64

Calcule un score de consensus entre différents algorithmes d'analyse.
Plus le score est élevé, plus les différentes méthodes sont en accord
sur les caractéristiques de la séquence.
"""
function calculate_multi_algorithm_consensus_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, all_metrics::Dict{String, Any})::Float64
    if isempty(sequence_history) || length(sequence_history) < 5
        return 0.0
    end

    # 1. Calculer différents scores avec les méthodes disponibles
    scores = Dict{String, Float64}()

    # Score de prédictibilité contextuelle
    context_score = calculate_context_predictability(calculator, sequence_history, all_metrics)
    scores["context"] = context_score

    # Score de force des patterns
    pattern_score = calculate_pattern_strength(calculator, sequence_history)
    scores["pattern"] = pattern_score

    # Score de compression
    compression_score = calculate_compression_score(calculator, sequence_history, all_metrics)
    scores["compression"] = compression_score

    # Score de divergence bayésienne (inverser pour cohérence)
    divergence_score = calculate_bayesian_divergence_score(calculator, sequence_history)
    scores["bayesian"] = 1.0 - divergence_score

    # 2. Calculer la variance des scores (faible variance = consensus élevé)
    score_values = collect(values(scores))
    if length(score_values) < 2
        return 0.0
    end

    mean_score = sum(score_values) / length(score_values)
    variance = sum((score - mean_score)^2 for score in score_values) / length(score_values)

    # 3. Convertir en score de consensus (faible variance = consensus élevé)
    consensus_score = 1.0 / (1.0 + variance * 10)  # Normalisation

    return round(consensus_score, digits=4)
end