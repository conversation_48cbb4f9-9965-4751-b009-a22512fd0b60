# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2758 à 2861
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

"""
    calculate_predictive_differentials(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, position::Int, analyzer::BaccaratEntropyAnalyzer)::Dict{String, Dict{String, Float64}}

Calcule les différentiels prédictifs pour les 9 valeurs INDEX5 possibles à la main n+1

ALGORITHME CORRIGÉ :
1. À la main n : Récupérer les métriques actuelles (Conditionnelle, Taux, DivEntropG, EntropG)
2. Pour chacune des 9 valeurs INDEX5 possibles à n+1 (selon règles INDEX1) :
   - Simuler l'ajout de cette valeur à la séquence
   - Calculer les nouvelles métriques pour la main n+1 simulée
   - Calculer |métrique(n+1) - métrique(n)| pour chaque métrique

CORRECTION : Utilise un cache pour éviter les recalculs entre tableaux différentiel et scores
"""
function calculate_predictive_differentials(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, position::Int, analyzer::BaccaratEntropyAnalyzer)::Dict{String, Dict{String, Float64}}
    if position > length(sequence) || position > length(evolution)
        return Dict{String, Dict{String, Float64}}()
    end

    # CORRECTION : Vérifier le cache d'abord
    cache_key = (Tuple(sequence), position, objectid(analyzer))
    if haskey(table._differential_cache, cache_key)
        return table._differential_cache[cache_key]
    end

    # CORRECTION : Pour prédire la main n+1, il faut utiliser la main n-1 comme référence pour les règles INDEX1
    if position == 1
        return Dict{String, Dict{String, Float64}}()  # Pas de prédiction possible pour la première main (pas de main précédente)
    end

    # ÉTAPE 1: Obtenir l'INDEX5 de référence (main précédente) et les métriques actuelles
    reference_index5 = sequence[position - 1]  # ✅ CORRECTION : Main précédente pour les règles INDEX1
    current_metrics = evolution[position]       # Métriques de la main actuelle

    # Métriques actuelles à la main n
    current_conditional = get(current_metrics, "conditional_entropy", 0.0)

    # CORRECTION HOMOGÈNE: Calculer le taux actuel sur bloc local [n-3,n-2,n-1,n]
    if length(sequence) >= 4
        local_block_current = sequence[position-3:position]  # [n-3,n-2,n-1,n]
        current_rate = _calculate_sequence_entropy_aep(analyzer, local_block_current)
    else
        current_rate = get(current_metrics, "entropy_rate", 0.0)
    end

    current_simple = get(current_metrics, "simple_entropy", 0.0)
    current_theoretical = get(current_metrics, "simple_entropy_theoretical", 0.0)

    # ÉTAPE 2: Calculer INDEX1 requis pour la main n+1 selon les règles déterministes
    # CORRECTION : Utiliser la main de référence (n-1) pour appliquer les règles INDEX1
    required_index1 = calculate_required_index1(table, reference_index5)
    if required_index1 === nothing
        return Dict{String, Dict{String, Float64}}()
    end

    # ÉTAPE 3: Obtenir les 9 valeurs INDEX5 possibles pour la main n+1
    valid_index5_values = get_valid_index5_values(table, required_index1)

    # ÉTAPE 4: Calculer les différentiels pour chaque valeur INDEX5 possible à n+1
    predictive_differentials = Dict{String, Dict{String, Float64}}()

    for possible_index5 in valid_index5_values
        try
            # ÉTAPE 4.1: Créer une séquence simulée avec la valeur possible ajoutée à n+1
            simulated_sequence = vcat(sequence[1:position], [possible_index5])

            # ÉTAPE 4.2: Calculer les métriques pour la main n+1 simulée

            # Entropie conditionnelle pour la séquence simulée complète
            simulated_conditional = _calculate_conditional_entropy(analyzer, simulated_sequence)

            # Entropie simple (Shannon) pour la séquence simulée complète
            # Calculer les fréquences de la séquence simulée
            counts = Dict{String, Int}()
            for value in simulated_sequence
                counts[value] = get(counts, value, 0) + 1
            end
            total = length(simulated_sequence)
            probabilities = [counts[value] / total for value in keys(counts)]
            simulated_simple = _calculate_shannon_entropy(analyzer, probabilities)

            # Entropie théorique (AEP) pour la séquence simulée complète
            simulated_theoretical = _calculate_sequence_entropy_aep(analyzer, simulated_sequence)

            # CORRECTION HOMOGÈNE: Taux d'entropie sur bloc local de longueur 4
            # Bloc actuel: [n-3,n-2,n-1,n] vs Bloc simulé: [n-2,n-1,n,n+1simulé]
            if length(simulated_sequence) >= 4
                # Calculer l'entropie AEP du bloc local de 4 éléments [n-2,n-1,n,n+1simulé]
                local_block_simulated = simulated_sequence[end-3:end]  # 4 derniers éléments
                simulated_rate = _calculate_sequence_entropy_aep(analyzer, local_block_simulated)
            else
                simulated_rate = simulated_simple
            end

            # ÉTAPE 4.3: Calculer les différentiels absolus |métrique(n+1) - métrique(n)|
            diff_cond = abs(simulated_conditional - current_conditional)
            diff_taux = abs(simulated_rate - current_rate)
            diff_div_entrop = abs(simulated_simple - current_simple)
            diff_entrop = abs(simulated_theoretical - current_theoretical)

        catch e
            # En cas d'erreur, utiliser des valeurs par défaut
            diff_cond = diff_taux = diff_div_entrop = diff_entrop = 0.0
        end

        predictive_differentials[possible_index5] = Dict{String, Float64}(
            "DiffCond" => diff_cond,
            "DiffTaux" => diff_taux,
            "DiffDivEntropG" => diff_div_entrop,
            "DiffEntropG" => diff_entrop
        )
    end

    # CORRECTION : Sauvegarder dans le cache avant de retourner
    table._differential_cache[cache_key] = predictive_differentials
    return predictive_differentials
end