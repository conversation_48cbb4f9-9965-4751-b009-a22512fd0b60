# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3041 à 3065
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    extract_index3(validator::INDEX5PredictionValidator, index5_value::Union{String, Nothing})::Union{String, Nothing}

Extrait INDEX3 d'une valeur INDEX5
Format: INDEX1_INDEX2_INDEX3 → retourne INDEX3
"""
function extract_index3(validator::INDEX5PredictionValidator, index5_value::Union{String, Nothing})::Union{String, Nothing}
    if index5_value === nothing || index5_value == "N/A"
        return nothing
    end

    # Nettoyer la valeur (enlever score de confiance si présent)
    clean_value = occursin('(', string(index5_value)) ? split(string(index5_value), '(')[1] : string(index5_value)

    # Diviser par underscore et prendre le dernier élément (INDEX3)
    parts = split(clean_value, '_')
    if length(parts) >= 3
        index3 = parts[3]  # INDEX3 (BANKER, PLAYER, TIE)

        # Normaliser les abréviations vers les formes complètes
        if index3 == "BANK"
            return "BANKER"
        elseif index3 == "PLAY"
            return "PLAYER"
        else
            return index3  # TIE reste TIE
        end
    end

    return nothing
end