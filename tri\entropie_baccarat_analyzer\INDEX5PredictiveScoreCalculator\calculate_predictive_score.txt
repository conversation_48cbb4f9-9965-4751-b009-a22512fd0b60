# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2409 à 2428
# Type: Méthode de la classe INDEX5PredictiveScoreCalculator

"""
    calculate_predictive_score(calculator::INDEX5PredictiveScoreCalculator, diff_c::Float64, diff_t::Float64, div_eg::Float64, ent_g::Float64)::Float64

Calcule le score prédictif selon la formule:
SCORE = (DiffC + EntG) / (DiffT + DivEG)

# Arguments
- `diff_c::Float64`: Différentiel Entropie Conditionnelle
- `diff_t::Float64`: Différentiel Taux d'Entropie
- `div_eg::Float64`: Différentiel Diversité Entropique
- `ent_g::Float64`: Différentiel Entropie Générale

# Returns
- `Float64`: Score prédictif (peut être infini si dénominateur = 0)
"""
function calculate_predictive_score(calculator::INDEX5PredictiveScoreCalculator, diff_c::Float64, diff_t::Float64, div_eg::Float64, ent_g::Float64)::Float64
    denominator = diff_t + div_eg

    if denominator == 0
        return Inf
    else
        return (diff_c + ent_g) / denominator
    end
end