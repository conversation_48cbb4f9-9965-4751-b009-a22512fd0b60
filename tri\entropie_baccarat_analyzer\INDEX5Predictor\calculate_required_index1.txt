# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2005 à 2025
# Type: Méthode de la classe INDEX5Predictor

"""
    calculate_required_index1(predictor::INDEX5Predictor, current_index5::String)::Union{Int, Nothing}

Calcule INDEX1 obligatoire selon les règles déterministes
Règles INDEX1:
- Si INDEX2=C: INDEX1 s'inverse (0→1, 1→0)
- Si INDEX2=A ou B: INDEX1 se conserve (0→0, 1→1)
"""
function calculate_required_index1(predictor::INDEX5Predictor, current_index5::String)::Union{Int, Nothing}
    if isempty(current_index5)
        return nothing
    end

    try
        current_parts = split(current_index5, '_')
        current_index1 = parse(Int, current_parts[1])
        current_index2 = current_parts[2]

        if current_index2 == "C"
            return 1 - current_index1  # Inversion obligatoire
        else  # A ou B
            return current_index1      # Conservation obligatoire
        end
    catch
        return nothing
    end
end