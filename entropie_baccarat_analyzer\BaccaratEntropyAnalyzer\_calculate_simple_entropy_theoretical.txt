# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 455 à 460
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _calculate_simple_entropy_theoretical(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64

Calcule l'entropie théorique simple - WRAPPER vers la méthode AEP unifiée.
Maintenu pour compatibilité avec le code existant.
"""
function _calculate_simple_entropy_theoretical(analyzer::BaccaratEntropyAnalyzer, sequence::Vector{String})::Float64
    return _calculate_sequence_entropy_aep(analyzer, sequence)
end