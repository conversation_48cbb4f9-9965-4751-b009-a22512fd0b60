# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2027 à 2038
# Type: Méthode de la classe INDEX5Predictor

    def get_valid_index5_values(self, required_index1):
        """
        Retourne tous les INDEX5 avec INDEX1 obligatoire
        """
        if required_index1 is None:
            return []

        valid_values = []
        for index2 in ['A', 'B', 'C']:
            for index3 in ['BANKER', 'PLAYER', 'TIE']:
                valid_values.append(f"{required_index1}_{index2}_{index3}")
        return valid_values