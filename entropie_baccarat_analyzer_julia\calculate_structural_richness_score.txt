# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1328 à 1367
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_structural_richness_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64

Calcule un score de richesse structurelle basé sur l'entropie topologique
et la diversité des patterns. Plus le score est élevé, plus la structure est riche.
"""
function calculate_structural_richness_score(calculator::INDEX5Calculator, sequence_history::Vector{String}, current_metrics::Dict{String, Any})::Float64
    if length(sequence_history) < 6
        return 0.0
    end

    # 1. Score basé sur l'entropie topologique si disponible
    topological_entropy = get(current_metrics, "topological_entropy", nothing)
    topo_score = 0.0

    if topological_entropy !== nothing
        # Normaliser l'entropie topologique (max théorique ≈ 4.17 pour INDEX5)
        topo_score = min(topological_entropy / 4.17, 1.0)
    end

    # 2. Score basé sur la diversité des patterns de différentes longueurs
    pattern_diversity_scores = Float64[]

    for pattern_len in 2:min(5, div(length(sequence_history), 2))
        patterns_found = Set{Tuple}()

        # Extraire tous les patterns de cette longueur
        for i in 1:(length(sequence_history) - pattern_len + 1)
            pattern = Tuple(sequence_history[i:i+pattern_len-1])
            push!(patterns_found, pattern)
        end

        # Score de diversité pour cette longueur
        max_possible_patterns = min(18^pattern_len, length(sequence_history) - pattern_len + 1)
        diversity_score = length(patterns_found) / max_possible_patterns
        push!(pattern_diversity_scores, diversity_score)
    end

    # 3. Score composite
    if !isempty(pattern_diversity_scores)
        avg_diversity = sum(pattern_diversity_scores) / length(pattern_diversity_scores)
        structural_richness = (0.6 * topo_score + 0.4 * avg_diversity)
    else
        structural_richness = topo_score
    end

    return round(structural_richness, digits=4)
end