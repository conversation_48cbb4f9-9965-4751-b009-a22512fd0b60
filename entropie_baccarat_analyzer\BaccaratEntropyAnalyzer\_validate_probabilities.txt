# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 136 à 157
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    _validate_probabilities(analyzer::BaccaratEntropyAnalyzer, p::Vector{Float64})::Vector{Float64}

✅ VALIDATION - Valide et normalise un vecteur de probabilités

Assure que les probabilités sont positives et normalisées (somme = 1).
Applique une distribution uniforme si toutes les probabilités sont nulles.

Référence: entropie/cours_entropie/ressources/implementations_python.py
"""
function _validate_probabilities(analyzer::BaccaratEntropyAnalyzer, p::Vector{Float64})::Vector{Float64}
    p = Float64.(p)

    if any(p .< 0)
        throw(ArgumentError("Les probabilités doivent être positives"))
    end

    total = sum(p)
    if total > 0
        p = p ./ total
    else
        # Distribution uniforme si toutes les probabilités sont nulles
        p = ones(length(p)) ./ length(p)
    end

    return p
end