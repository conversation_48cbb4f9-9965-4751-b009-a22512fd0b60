# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1820 à 1846
# Type: Méthode de la classe INDEX5Predictor

"""
    predict_entropy_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, entropy_evolution::Vector{Dict{String, Any}})::Union{String, Nothing}

Prédiction basée sur l'analyse entropique avancée
Utilise entropie métrique, complexité LZ, entropie topologique
"""
function predict_entropy_level(predictor::INDEX5Predictor, sequence_history::Vector{String}, entropy_evolution::Vector{Dict{String, Any}})::Union{String, Nothing}
    if isempty(entropy_evolution)
        return nothing
    end

    current_metrics = !isempty(entropy_evolution) ? entropy_evolution[end] : Dict{String, Any}()

    # 1. Si entropie métrique stable → Système déterministe
    recent_evolution = length(entropy_evolution) >= 10 ? entropy_evolution[end-9:end] : entropy_evolution
    if is_metric_entropy_stable(predictor, recent_evolution)
        # Utiliser modèle déterministe basé sur transitions
        return predict_deterministic_model(predictor, sequence_history)
    end

    # 2. Si complexité LZ faible → Séquence compressible
    if get(current_metrics, "lz_complexity", 100) < 35
        # Exploiter patterns de compression
        return predict_compression_patterns(predictor, sequence_history)
    end

    # 3. Si entropie topologique élevée → Richesse structurelle
    # CORRECTION AEP: Nouveau seuil basé sur les valeurs observées (3.8-4.1 bits)
    if get(current_metrics, "topological_entropy", 0) > 4.05
        # Modèle sophistiqué multi-patterns
        return predict_rich_structure_model(predictor, sequence_history)
    end

    return nothing
end