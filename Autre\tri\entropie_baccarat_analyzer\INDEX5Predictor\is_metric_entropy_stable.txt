# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1848 à 1858
# Type: Méthode de la classe INDEX5Predictor

"""
    is_metric_entropy_stable(predictor::INDEX5Predictor, recent_entropy_evolution::Vector{Dict{String, Any}})::Bool

Vérifie si l'entropie métrique est stable
"""
function is_metric_entropy_stable(predictor::INDEX5Predictor, recent_entropy_evolution::Vector{Dict{String, Any}})::Bool
    if length(recent_entropy_evolution) < 5
        return false
    end

    metric_entropies = [get(item, "metric_entropy", 0) for item in recent_entropy_evolution]
    variance = length(metric_entropies) > 0 ? var(metric_entropies) : 0

    return variance < 0.1  # Seuil de stabilité
end