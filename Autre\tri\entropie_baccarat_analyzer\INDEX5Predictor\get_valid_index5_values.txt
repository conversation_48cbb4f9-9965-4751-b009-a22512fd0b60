# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2027 à 2038
# Type: Méthode de la classe INDEX5Predictor

"""
    get_valid_index5_values(predictor::INDEX5Predictor, required_index1::Union{Int, Nothing})::Vector{String}

Retourne tous les INDEX5 avec INDEX1 obligatoire
"""
function get_valid_index5_values(predictor::INDEX5Predictor, required_index1::Union{Int, Nothing})::Vector{String}
    if required_index1 === nothing
        return String[]
    end

    valid_values = String[]
    for index2 in ["A", "B", "C"]
        for index3 in ["BANKER", "PLAYER", "TIE"]
            push!(valid_values, "$(required_index1)_$(index2)_$(index3)")
        end
    end
    return valid_values
end