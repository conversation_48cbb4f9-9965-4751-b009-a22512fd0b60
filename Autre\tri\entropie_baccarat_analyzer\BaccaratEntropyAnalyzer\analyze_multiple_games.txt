# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1074 à 1121
# Type: Méthode de la classe BaccaratEntropyAnalyzer

"""
    analyze_multiple_games(analyzer::BaccaratEntropyAnalyzer, data::Vector{Dict{String, Any}}, max_games::Union{Int, Nothing}=nothing)::Dict{String, Any}

Analyse multiple parties et calcule des statistiques globales.

# Arguments
- `data::Vector{Dict{String, Any}}`: Liste des données de parties
- `max_games::Union{Int, Nothing}`: Nombre maximum de parties à analyser (optionnel)

# Returns
- `Dict{String, Any}`: Statistiques globales d'analyse
"""
function analyze_multiple_games(analyzer::BaccaratEntropyAnalyzer, data::Vector{Dict{String, Any}}, max_games::Union{Int, Nothing}=nothing)::Dict{String, Any}
    if max_games !== nothing
        data = data[1:min(max_games, length(data))]
    end

    all_results = Dict{String, Any}[]
    successful_analyses = 0

    println("🔄 Analyse de $(length(data)) parties...")

    for (i, game_data) in enumerate(data)
        game_id = "Game_$i"
        result = analyze_single_game(analyzer, game_data, game_id)

        if !haskey(result, "error")
            push!(all_results, result)
            successful_analyses += 1
        end

        if i % 10 == 0
            println("   Progression: $i/$(length(data)) parties analysées")
        end
    end

    if isempty(all_results)
        return Dict{String, Any}("error" => "Aucune partie analysée avec succès")
    end

    # Calcul des statistiques globales
    final_entropies = [result["final_metric_entropy"] for result in all_results]
    sequence_lengths = [result["sequence_length"] for result in all_results]
    max_entropy_positions = [result["max_metric_entropy_position"] for result in all_results]

    return Dict{String, Any}(
        "total_games_analyzed" => successful_analyses,
        "average_final_entropy" => mean(final_entropies),
        "std_final_entropy" => std(final_entropies),
        "min_final_entropy" => minimum(final_entropies),
        "max_final_entropy" => maximum(final_entropies),
        "average_sequence_length" => mean(sequence_lengths),
        "average_max_entropy_position" => mean(max_entropy_positions),
        "all_results" => all_results
    )
end