# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3033 à 3039
# Type: Méthode de la classe INDEX5PredictionValidator
# Note: Cette méthode est homonyme (même nom que d'autres méthodes)

"""
    INDEX5PredictionValidator()

Initialisation du validateur de prédictions
"""
mutable struct INDEX5PredictionValidator
    correct_predictions::Int
    total_predictions::Int
    correct_predictions_high_confidence::Int  # Nouveau compteur pour poids >= 60%
    total_predictions_high_confidence::Int    # Total prédictions avec poids >= 60%
    prediction_details::Vector{Any}

    function INDEX5PredictionValidator()
        new(0, 0, 0, 0, Any[])
    end
end