# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 193 à 228
# Type: Méthode de la classe BaccaratEntropyAnalyzer

    def load_baccarat_data(self, filepath: str) -> List[Dict]:
        """
        📁 CHARGEMENT - Charge les données de baccarat depuis le fichier JSON

        Gère différentes structures JSON possibles et valide les données chargées.

        Args:
            filepath: Chemin vers le fichier JSON

        Returns:
            Liste des parties de baccarat
        """
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Vérifier la structure du JSON
            if isinstance(data, dict) and 'parties_condensees' in data:
                # Structure: {"parties_condensees": [...]}
                parties = data['parties_condensees']
                print(f"✅ Données chargées: {len(parties)} parties trouvées")
                return parties
            elif isinstance(data, list):
                # Structure: [partie1, partie2, ...]
                print(f"✅ Données chargées: {len(data)} parties trouvées")
                return data
            else:
                print(f"❌ Structure JSON non reconnue. Clés disponibles: {list(data.keys()) if isinstance(data, dict) else 'Liste non détectée'}")
                return []

        except FileNotFoundError:
            print(f"❌ Erreur: Fichier {filepath} non trouvé")
            return []
        except json.JSONDecodeError as e:
            print(f"❌ Erreur JSON: {e}")
            return []