# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2181 à 2213
# Type: Méthode de la classe INDEX5Predictor

"""
    predict_bayesian_theoretical(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}

Combine observations avec probabilités théoriques INDEX5
"""
function predict_bayesian_theoretical(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}
    # Calculer fréquences observées récentes (20 dernières mains)
    recent_sequence = length(sequence_history) >= 20 ? sequence_history[end-19:end] : sequence_history

    # Compter les fréquences observées
    observed_freq = Dict{String, Int}()
    for value in recent_sequence
        observed_freq[value] = get(observed_freq, value, 0) + 1
    end

    bayesian_probs = Dict{String, Float64}()

    for index5_value in keys(predictor.THEORETICAL_PROBS)
        # Probabilité théorique
        p_theoretical = predictor.THEORETICAL_PROBS[index5_value]

        # Probabilité observée (avec lissage de Laplace)
        observed_count = get(observed_freq, index5_value, 0)
        p_observed = (observed_count + 1) / (length(recent_sequence) + length(predictor.THEORETICAL_PROBS))

        # Fusion Bayésienne (pondération adaptative selon la prédictibilité)
        predictability = get(metrics, "predictability_score", 0.5)

        # Plus c'est prévisible, plus on fait confiance aux observations
        bayesian_prob = (predictability * p_observed +
                        (1 - predictability) * p_theoretical)

        bayesian_probs[index5_value] = bayesian_prob
    end

    if !isempty(bayesian_probs)
        return argmax(bayesian_probs)
    end

    return nothing
end