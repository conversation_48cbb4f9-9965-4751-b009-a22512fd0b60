# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1584 à 1629
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_transition_matrix_entropy(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64

Calcule l'entropie de la matrice de transitions INDEX5.
Plus l'entropie est faible, plus les transitions sont prévisibles.
"""
function calculate_transition_matrix_entropy(calculator::INDEX5Calculator, sequence_history::Vector{String})::Float64
    if length(sequence_history) < 3
        return 0.0
    end

    # Construire la matrice de transitions
    transitions = Dict{String, Dict{String, Int}}()

    for i in 1:(length(sequence_history) - 1)
        current = sequence_history[i]
        next_val = sequence_history[i + 1]

        if current ∉ keys(transitions)
            transitions[current] = Dict{String, Int}()
        end

        if next_val ∉ keys(transitions[current])
            transitions[current][next_val] = 0
        end
        transitions[current][next_val] += 1
    end

    # Calculer l'entropie de chaque ligne de la matrice
    total_entropy = 0.0
    total_weight = 0.0

    for (current_state, next_states) in transitions
        state_total = sum(values(next_states))
        state_weight = state_total / (length(sequence_history) - 1)

        # CORRECTION AEP: Entropie pour cet état selon AEP
        # Créer la séquence des états suivants pour cet état
        state_sequence = String[]
        for (next_state, count) in next_states
            append!(state_sequence, repeat([next_state], count))
        end

        # Appeler la méthode depuis l'analyzer
        if calculator.analyzer !== nothing
            state_entropy = _calculate_sequence_entropy_aep(calculator.analyzer, state_sequence)
        else
            # Fallback si pas d'analyzer (ne devrait pas arriver)
            state_entropy = 0.0
        end

        total_entropy += state_entropy * state_weight
        total_weight += state_weight
    end

    if total_weight > 0
        return round(total_entropy / total_weight, digits=4)
    end

    return 0.0
end