# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1866 à 1879
# Type: Méthode de la classe INDEX5Predictor

"""
    predict_compression_patterns(predictor::INDEX5Predictor, sequence_history::Vector{String})::Union{String, Nothing}

Exploite les patterns de compression pour prédiction
"""
function predict_compression_patterns(predictor::INDEX5Predictor, sequence_history::Vector{String})::Union{String, Nothing}
    # Chercher les patterns répétitifs les plus récents
    for pattern_len in 2:min(5, length(sequence_history)-1)
        recent_pattern = sequence_history[end-pattern_len+1:end]

        # Chercher ce pattern dans l'historique
        continuation = find_exact_pattern_continuation(predictor, recent_pattern, sequence_history)
        if continuation !== nothing
            return continuation
        end
    end

    return nothing
end