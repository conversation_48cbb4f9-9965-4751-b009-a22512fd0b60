# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1237 à 1251
# Type: Méthode de la classe INDEX5Calculator

"""
    count_pattern_occurrences(calculator::INDEX5Calculator, pattern::Vector{String}, sequence_history::Vector{String})::Int

Compte le nombre d'occurrences d'un pattern dans l'historique
"""
function count_pattern_occurrences(calculator::INDEX5Calculator, pattern::Vector{String}, sequence_history::Vector{String})::Int
    if length(pattern) == 0
        return 0
    end

    pattern_len = length(pattern)
    count = 0

    for i in 1:(length(sequence_history) - pattern_len + 1)
        if sequence_history[i:i+pattern_len-1] == pattern
            count += 1
        end
    end

    return count
end