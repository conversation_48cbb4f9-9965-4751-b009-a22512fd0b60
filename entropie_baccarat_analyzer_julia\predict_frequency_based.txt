# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2249 à 2263
# Type: Méthode de la classe INDEX5Predictor

"""
    predict_frequency_based(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}

Prédiction basée sur les fréquences observées
"""
function predict_frequency_based(predictor::INDEX5Predictor, sequence_history::Vector{String}, metrics::Dict{String, Any})::Union{String, Nothing}
    # Analyser les fréquences récentes
    recent_sequence = length(sequence_history) >= 30 ? sequence_history[end-29:end] : sequence_history

    # Compter les fréquences
    freq_counter = Dict{String, Int}()
    for value in recent_sequence
        freq_counter[value] = get(freq_counter, value, 0) + 1
    end

    if !isempty(freq_counter)
        # Retourner la valeur la plus fréquente récemment
        return argmax(freq_counter)
    end

    return nothing
end