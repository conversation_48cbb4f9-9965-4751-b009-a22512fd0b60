# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 1278 à 1299
# Type: Méthode de la classe INDEX5Calculator

"""
    calculate_entropy_stability_score(calculator::INDEX5Calculator, entropy_evolution::Vector{Dict{String, Any}})::Float64

Calcule un score de stabilité de l'entropie métrique.
Plus le score est élevé, plus l'entropie est stable (système déterministe).
"""
function calculate_entropy_stability_score(calculator::INDEX5Calculator, entropy_evolution::Vector{Dict{String, Any}})::Float64
    if length(entropy_evolution) < 5
        return 0.0
    end

    # Prendre les 10 dernières valeurs d'entropie métrique
    recent_evolution = length(entropy_evolution) >= 10 ? entropy_evolution[end-9:end] : entropy_evolution
    metric_entropies = [get(item, "metric_entropy", 0) for item in recent_evolution]

    if length(metric_entropies) < 2
        return 0.0
    end

    # Calculer la variance (plus faible = plus stable)
    variance = length(metric_entropies) > 0 ? var(metric_entropies) : 0.0

    # Convertir en score de stabilité (inverse de la variance, normalisé)
    stability_score = 1.0 / (1.0 + variance * 10)  # Normalisation

    return round(stability_score, digits=4)
end