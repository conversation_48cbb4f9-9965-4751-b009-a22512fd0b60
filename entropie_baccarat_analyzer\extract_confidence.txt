# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 3067 à 3080
# Type: Méthode de la classe INDEX5PredictionValidator

"""
    extract_confidence(validator::INDEX5PredictionValidator, predicted_index5::Union{String, Nothing})::Float64

Extrait le score de confiance d'une prédiction
Format: INDEX5(0.XX) → retourne 0.XX comme float
"""
function extract_confidence(validator::INDEX5PredictionValidator, predicted_index5::Union{String, Nothing})::Float64
    if predicted_index5 === nothing || !occursin('(', predicted_index5)
        return 0.0
    end

    try
        # Extraire la partie entre parenthèses
        confidence_part = split(split(predicted_index5, '(')[2], ')')[1]
        return parse(Float64, confidence_part)
    catch e
        if isa(e, BoundsError) || isa(e, ArgumentError)
            return 0.0
        else
            rethrow(e)
        end
    end
end