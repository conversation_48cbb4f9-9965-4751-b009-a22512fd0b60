# Emplacement exact dans le fichier source:
# Fichier: C:\Users\<USER>\Desktop\7\entropie_baccarat_analyzer.py
# Lignes: 2863 à 2964
# Type: Méthode de la classe INDEX5PredictiveDifferentialTable

"""
    generate_predictive_table_part(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, start_main::Int, end_main::Int, part_number::Int)::String

Génère une partie du tableau prédictif avec différentiels et séparateurs verticaux
"""
function generate_predictive_table_part(table::INDEX5PredictiveDifferentialTable, sequence::Vector{String}, evolution::Vector{Dict{String, Any}}, analyzer::BaccaratEntropyAnalyzer, start_main::Int, end_main::Int, part_number::Int)::String
    if isempty(sequence) || isempty(evolution)
        return "❌ Données insuffisantes pour générer le tableau prédictif"
    end

    # Pour le premier tableau, commencer à la main 6 au lieu de la main 1
    if part_number == 1
        actual_start = max(start_main, 6)  # Commencer à la main 6
    else
        actual_start = start_main
    end

    # Ajuster les limites selon la longueur de la séquence
    actual_end = min(end_main, length(sequence))
    if actual_start > length(sequence)
        return "❌ Main $actual_start dépasse la longueur de la séquence ($(length(sequence)) mains)"
    end

    # En-tête du tableau avec séparateurs verticaux
    header_line1 = rpad("INDEX5 n+1", 15)
    header_line2 = " " ^ 15
    header_separator = " " ^ 15  # Séparateur horizontal sous les en-têtes Main
    separator_line = "=" ^ 15

    # Générer l'en-tête pour la plage de mains spécifiée avec séparateurs
    for i in actual_start:actual_end
        header_line1 *= "|" * rpad("Main $i", 24)
        # Construction exacte du format souhaité : |DiffC|DiffT|DivEG|EntG  |
        header_line2 *= rpad("|DiffC|DiffT|DivEG|EntG  ", 25)
        # Séparateur horizontal sous chaque "Main X" mais pas sous "INDEX5 n+1"
        header_separator *= "|" * "-" ^ 24
        separator_line *= "|" * "=" ^ 24
    end

    # Ajouter le séparateur final
    header_line1 *= "|"
    header_line2 *= "|"
    header_separator *= "|"
    separator_line *= "|"

    table = "📊 PARTIE $part_number - MAINS $actual_start À $actual_end\n"
    table *= separator_line * "\n"
    table *= header_line1 * "\n"
    table *= header_separator * "\n"  # Séparateur horizontal entre Main et DiffC DiffT DivEG EntG
    table *= header_line2 * "\n"
    table *= separator_line * "\n"

    # Générer les lignes pour chaque valeur INDEX5 possible avec séparateurs
    for (i, index5_value) in enumerate(table.all_index5_values)
        line = rpad(index5_value, 15)

        for position in (actual_start - 1):(actual_end - 1)  # -1 car les indices commencent à 1 en Julia
            line *= "|"  # Séparateur vertical avant chaque main

            if position + 1 <= length(sequence)  # +1 car position est 0-indexé
                # Calculer les différentiels prédictifs
                predictive_diffs = calculate_predictive_differentials(
                    table, sequence, evolution, position + 1, analyzer  # +1 pour convertir en 1-indexé
                )

                if haskey(predictive_diffs, index5_value)
                    diffs = predictive_diffs[index5_value]
                    # Ajouter des séparateurs verticaux entre les valeurs
                    cell_content = @sprintf("%.3f|%.3f|%.3f|%.3f",
                        diffs["DiffCond"], diffs["DiffTaux"],
                        diffs["DiffDivEntropG"], diffs["DiffEntropG"])
                else
                    cell_content = "N/A  |N/A  |N/A  |N/A   "
                end
            else
                cell_content = "---  |---  |---  |---   "
            end

            # Centrer le contenu dans 24 caractères
            line *= lpad(rpad(cell_content, 12), 24)
        end

        line *= "|"  # Séparateur final
        table *= line * "\n"

        # Ajouter un séparateur horizontal entre 0_C_TIE et 1_A_BANKER
        if index5_value == "0_C_TIE"
            table *= separator_line * "\n"
        end
    end

    # Ajouter une ligne de séparation finale
    table *= separator_line * "\n"

    # NOUVELLE LIGNE : Valeur INDEX5 réellement observée pour chaque main
    observed_line = "OBSERVÉ        "
    for position in (actual_start - 1):(actual_end - 1)  # -1 car les indices commencent à 1 en Julia
        observed_line *= "|"  # Séparateur vertical avant chaque main

        if position + 1 <= length(sequence)  # +1 car position est 0-indexé
            # Récupérer la valeur INDEX5 réellement observée à cette position
            observed_index5 = sequence[position + 1]  # +1 pour convertir en 1-indexé
            # Aligner la valeur observée à gauche dans la cellule
            cell_content = observed_index5
        else
            cell_content = "---"
        end

        observed_line *= rpad(cell_content, 24)
    end

    observed_line *= "|\n"
    table *= observed_line

    # Ligne de séparation finale après la ligne observée
    table *= separator_line * "\n"

    return table
end